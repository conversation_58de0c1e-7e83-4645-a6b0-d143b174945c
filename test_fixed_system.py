#!/usr/bin/env python3
"""
Test Fixed System
================

Test the fixed EMA crossover system to verify signals are generated.
"""

import sys
import os
import time
import json
from datetime import datetime

# Add src directory to path
sys.path.append('src')

from ema import EMACalculator
from strategy import EMAStrategy
from logger import SignalLogger
from market_feed import MockMarketFeed


def test_fixed_system():
    """Test the complete fixed system"""
    print("🧪 Testing Fixed EMA Crossover System...")

    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)

    # Use simpler configuration for testing
    test_config = {
        'ema_combinations': [{'short_ema': 5, 'long_ema': 10}],
        'timeframes': ['1min'],
        'instrument': config['instrument']
    }

    # Create components
    ema_calculator = EMACalculator(test_config['ema_combinations'])
    signal_logger = SignalLogger("test_data", test_config['timeframes'], 100000)

    strategy = EMAStrategy(
        ema_combinations=test_config['ema_combinations'],
        timeframes=test_config['timeframes'],
        ema_calculator=ema_calculator,
        signal_logger=signal_logger
    )

    # Create mock feed with faster tick rate
    mock_feed = MockMarketFeed(test_config['instrument'], strategy)
    mock_feed.tick_interval = 0.1  # 10 ticks per second

    print("  Starting mock feed for 60 seconds...")
    print("  This should generate trending price movements and EMA crossovers...")
    print("  Need at least 10 candles for 10-period EMA initialization...")

    # Start mock feed
    mock_feed.start()

    # Monitor for 60 seconds to get enough candles
    start_time = time.time()
    last_stats_time = start_time

    while time.time() - start_time < 60:
        # Print stats every 5 seconds
        if time.time() - last_stats_time >= 5:
            strategy_stats = strategy.get_statistics()
            logger_stats = signal_logger.get_statistics()
            feed_stats = mock_feed.get_statistics()

            print(f"  Progress: {time.time() - start_time:.0f}s - "
                  f"Ticks: {feed_stats['ticks_sent']}, "
                  f"Candles: {sum(strategy_stats['candles_generated'].values())}, "
                  f"Signals: {logger_stats['total_signals']}")

            last_stats_time = time.time()

        time.sleep(1)

    # Stop mock feed
    mock_feed.stop()

    # Force completion of any pending candles
    strategy.force_candle_completion()

    # Get final statistics
    strategy_stats = strategy.get_statistics()
    logger_stats = signal_logger.get_statistics()
    feed_stats = mock_feed.get_statistics()

    print(f"\n📊 Final Results:")
    print(f"  Ticks sent: {feed_stats['ticks_sent']}")
    print(f"  Ticks processed: {strategy_stats['ticks_processed']}")
    print(f"  Candles generated: {dict(strategy_stats['candles_generated'])}")
    print(f"  Signals generated: {dict(strategy_stats['signals_generated'])}")
    print(f"  Total signals: {logger_stats['total_signals']}")

    # Check results
    success = True

    if feed_stats['ticks_sent'] < 50:
        print("❌ Not enough ticks generated")
        success = False

    if strategy_stats['ticks_processed'] < feed_stats['ticks_sent'] * 0.9:
        print("❌ Not all ticks were processed")
        success = False

    if sum(strategy_stats['candles_generated'].values()) < 10:
        print("❌ Not enough candles generated")
        success = False

    if logger_stats['total_signals'] == 0:
        print("❌ No signals generated")
        success = False
    else:
        print(f"✅ Generated {logger_stats['total_signals']} signals!")

        # Show signal details
        for tf, stats in logger_stats['timeframes'].items():
            if stats['signal_count'] > 0:
                print(f"  {tf}: {stats['signal_count']} signals, P&L: {stats['cumulative_pnl']:.2f}")

    # Close logger
    signal_logger.close()

    return success


def main():
    """Run the test"""
    print("=" * 60)
    print("TESTING FIXED EMA CROSSOVER SYSTEM")
    print("=" * 60)

    try:
        # Create test directories
        os.makedirs("test_data", exist_ok=True)

        # Run test
        success = test_fixed_system()

        print("\n" + "=" * 60)
        if success:
            print("🎉 SUCCESS! The fixed system is working correctly!")
            print("\nThe heartbeat error has been fixed and signals are being generated.")
            print("\nYou can now run the live system:")
            print("  python src/main.py")
        else:
            print("⚠️  Some issues remain. Check the output above for details.")

        print("\nCheck test_data/ directory for generated CSV files.")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
