#!/usr/bin/env python3
"""
DhanHQ Market Feed Module
========================

This module handles real-time market data streaming from DhanHQ using WebSocket.
It connects to DhanHQ's live market feed, subscribes to NIFTY 50 data,
and forwards tick data to the trading strategy for processing.

Key Features:
- WebSocket connection to DhanHQ live market feed
- Automatic reconnection on disconnects
- NIFTY 50 instrument subscription
- Real-time tick data processing
- Error handling and logging
- Connection status monitoring

Author: AI Assistant
Date: 2025
"""

import json
import logging
import time
import threading
from datetime import datetime
from typing import Dict, Optional, Callable
# Optional imports for DhanHQ integration
try:
    import websocket
    from dhanhq import dhanhq
    DHANHQ_AVAILABLE = True
except ImportError:
    websocket = None
    dhanhq = None
    DHANHQ_AVAILABLE = False


class DhanMarketFeed:
    """
    DhanHQ WebSocket Market Feed Client

    Connects to DhanHQ's live market feed and streams real-time tick data
    for NIFTY 50 to the trading strategy.
    """

    def __init__(self, client_id: str, access_token: str, instrument: Dict, strategy):
        """
        Initialize DhanHQ market feed

        Args:
            client_id: DhanHQ client ID
            access_token: DhanHQ access token
            instrument: Instrument configuration
            strategy: Strategy instance to receive tick data
        """
        if not DHANHQ_AVAILABLE:
            raise ImportError("DhanHQ and websocket libraries are required for live market feed. "
                            "Install with: pip install dhanhq websocket-client")

        self.client_id = client_id
        self.access_token = access_token
        self.instrument = instrument
        self.strategy = strategy

        # DhanHQ client
        self.dhan = dhanhq(client_id, access_token)

        # WebSocket connection
        self.ws = None
        self.ws_url = "wss://api.dhan.co"

        # Connection state
        self.connected = False
        self.running = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 5  # seconds

        # Statistics
        self.stats = {
            'ticks_received': 0,
            'connection_time': None,
            'last_tick_time': None,
            'reconnections': 0
        }

        # Threading
        self.ws_thread = None
        self.heartbeat_thread = None

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"DhanHQ Market Feed initialized for {instrument['name']}")

    def start(self):
        """Start the market feed"""
        self.logger.info("Starting DhanHQ market feed...")
        self.running = True

        # Validate instrument
        if not self._validate_instrument():
            return False

        # Start WebSocket connection
        self._connect_websocket()

        # Start heartbeat thread
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()

        return True

    def stop(self):
        """Stop the market feed"""
        self.logger.info("Stopping DhanHQ market feed...")
        self.running = False

        if self.ws:
            self.ws.close()

        self.connected = False
        self.logger.info("Market feed stopped")

    def _validate_instrument(self) -> bool:
        """Validate instrument configuration"""
        try:
            # For now, we'll use the configured security ID
            # In a real implementation, you might want to fetch and validate from DhanHQ
            security_id = self.instrument.get('security_id')
            exchange_segment = self.instrument.get('exchange_segment')

            if not security_id or not exchange_segment:
                self.logger.error("Invalid instrument configuration")
                return False

            self.logger.info(f"Validated instrument: {self.instrument['name']} "
                           f"(ID: {security_id}, Exchange: {exchange_segment})")
            return True

        except Exception as e:
            self.logger.error(f"Error validating instrument: {e}")
            return False

    def _connect_websocket(self):
        """Connect to DhanHQ WebSocket"""
        try:
            self.logger.info("Connecting to DhanHQ WebSocket...")

            # Create WebSocket connection
            websocket.enableTrace(False)  # Set to True for debugging

            self.ws = websocket.WebSocketApp(
                self.ws_url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )

            # Start WebSocket in a separate thread
            self.ws_thread = threading.Thread(
                target=self.ws.run_forever,
                kwargs={'ping_interval': 30, 'ping_timeout': 10},
                daemon=True
            )
            self.ws_thread.start()

        except Exception as e:
            self.logger.error(f"Error connecting to WebSocket: {e}")
            self._schedule_reconnect()

    def _on_open(self, ws):
        """WebSocket connection opened"""
        self.logger.info("✅ WebSocket connected to DhanHQ")
        self.connected = True
        self.reconnect_attempts = 0
        self.stats['connection_time'] = datetime.now()

        # Subscribe to instrument
        self._subscribe_to_instrument()

    def _on_message(self, ws, message):
        """Handle incoming WebSocket message"""
        try:
            # Parse message
            data = json.loads(message)

            # Process tick data
            self._process_tick_data(data)

        except json.JSONDecodeError as e:
            self.logger.warning(f"Invalid JSON message: {e}")
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    def _on_error(self, ws, error):
        """Handle WebSocket error"""
        self.logger.error(f"WebSocket error: {error}")
        self.connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        self.logger.warning(f"WebSocket closed: {close_status_code} - {close_msg}")
        self.connected = False

        if self.running:
            self._schedule_reconnect()

    def _subscribe_to_instrument(self):
        """Subscribe to instrument data"""
        try:
            # Prepare subscription message
            subscription_msg = {
                "RequestCode": 15,
                "InstrumentCount": 1,
                "InstrumentList": [
                    {
                        "ExchangeSegment": self.instrument['exchange_segment'],
                        "SecurityId": self.instrument['security_id']
                    }
                ]
            }

            # Send subscription
            self.ws.send(json.dumps(subscription_msg))
            self.logger.info(f"Subscribed to {self.instrument['name']} "
                           f"(ID: {self.instrument['security_id']})")

        except Exception as e:
            self.logger.error(f"Error subscribing to instrument: {e}")

    def _process_tick_data(self, data: Dict):
        """Process incoming tick data"""
        try:
            # Update statistics
            self.stats['ticks_received'] += 1
            self.stats['last_tick_time'] = datetime.now()

            # Extract relevant data (this structure may vary based on DhanHQ's actual format)
            # For now, we'll simulate the expected structure
            if 'type' in data and data['type'] == 'tick':
                tick_data = {
                    'timestamp': data.get('timestamp', time.time()),
                    'price': data.get('ltp', data.get('price', 0)),  # Last Traded Price
                    'volume': data.get('volume', 1),
                    'security_id': data.get('security_id'),
                    'exchange_segment': data.get('exchange_segment')
                }

                # Validate tick data
                if tick_data['price'] > 0:
                    # Forward to strategy
                    self.strategy.process_tick(tick_data)

                    self.logger.debug(f"Processed tick: {tick_data}")
                else:
                    self.logger.warning(f"Invalid tick price: {tick_data}")

        except Exception as e:
            self.logger.error(f"Error processing tick data: {e}")

    def _schedule_reconnect(self):
        """Schedule WebSocket reconnection"""
        if not self.running or self.reconnect_attempts >= self.max_reconnect_attempts:
            return

        self.reconnect_attempts += 1
        self.stats['reconnections'] += 1

        self.logger.info(f"Scheduling reconnection attempt {self.reconnect_attempts} "
                        f"in {self.reconnect_delay} seconds...")

        # Reconnect after delay
        threading.Timer(self.reconnect_delay, self._connect_websocket).start()

    def _heartbeat_loop(self):
        """Heartbeat loop to monitor connection"""
        while self.running:
            try:
                if self.connected and self.ws:
                    # Send ping to keep connection alive
                    self.ws.ping()

                time.sleep(30)  # Heartbeat every 30 seconds

            except Exception as e:
                self.logger.error(f"Heartbeat error: {e}")
                time.sleep(5)

    def get_connection_status(self) -> Dict:
        """Get connection status and statistics"""
        return {
            'connected': self.connected,
            'running': self.running,
            'reconnect_attempts': self.reconnect_attempts,
            'stats': self.stats.copy()
        }

    def is_connected(self) -> bool:
        """Check if WebSocket is connected"""
        return self.connected and self.ws is not None

    def get_statistics(self) -> Dict:
        """Get market feed statistics"""
        return self.stats.copy()


# Mock market feed for testing without DhanHQ connection
class MockMarketFeed:
    """
    Mock market feed for testing and development

    Simulates real-time NIFTY 50 tick data for testing the EMA strategy
    without requiring actual DhanHQ connection.
    """

    def __init__(self, instrument: Dict, strategy):
        """Initialize mock market feed"""
        self.instrument = instrument
        self.strategy = strategy
        self.running = False

        # Simulation parameters
        self.base_price = 19500.0  # Starting NIFTY 50 price
        self.current_price = self.base_price
        self.tick_interval = 1.0  # seconds between ticks

        # Statistics
        self.stats = {
            'ticks_sent': 0,
            'start_time': None,
            'last_tick_time': None
        }

        self.logger = logging.getLogger(__name__)
        self.logger.info("Mock Market Feed initialized for testing")

    def start(self):
        """Start mock market feed"""
        self.logger.info("Starting mock market feed...")
        self.running = True
        self.stats['start_time'] = datetime.now()

        # Start simulation thread
        simulation_thread = threading.Thread(target=self._simulate_ticks, daemon=True)
        simulation_thread.start()

        return True

    def stop(self):
        """Stop mock market feed"""
        self.logger.info("Stopping mock market feed...")
        self.running = False

    def _simulate_ticks(self):
        """Simulate real-time tick data"""
        import random

        while self.running:
            try:
                # Generate random price movement
                change_percent = random.uniform(-0.001, 0.001)  # ±0.1% change
                self.current_price *= (1 + change_percent)

                # Create tick data
                tick_data = {
                    'timestamp': time.time(),
                    'price': round(self.current_price, 2),
                    'volume': random.randint(1, 100),
                    'security_id': self.instrument['security_id'],
                    'exchange_segment': self.instrument['exchange_segment']
                }

                # Send to strategy
                self.strategy.process_tick(tick_data)

                # Update statistics
                self.stats['ticks_sent'] += 1
                self.stats['last_tick_time'] = datetime.now()

                self.logger.debug(f"Mock tick: {tick_data}")

                # Wait for next tick
                time.sleep(self.tick_interval)

            except Exception as e:
                self.logger.error(f"Error in mock simulation: {e}")
                time.sleep(1)

    def get_connection_status(self) -> Dict:
        """Get mock connection status"""
        return {
            'connected': self.running,
            'running': self.running,
            'reconnect_attempts': 0,
            'stats': self.stats.copy()
        }

    def is_connected(self) -> bool:
        """Check if mock feed is running"""
        return self.running

    def get_statistics(self) -> Dict:
        """Get mock feed statistics"""
        return self.stats.copy()