#!/usr/bin/env python3
"""
EMA Crossover Strategy Module
============================

This module implements the EMA crossover trading strategy logic.
It processes real-time market data, generates synthetic OHLC candles,
calculates EMAs, detects crossover signals, and triggers logging.

Key Features:
- Multi-timeframe OHLC candle generation from tick data
- EMA crossover signal detection (Golden Cross & Death Cross)
- Real-time signal processing and logging
- Position tracking and P&L calculation
- Configurable EMA combinations and timeframes

Author: AI Assistant
Date: 2025
"""

import logging
from datetime import datetime, timedelta
from collections import defaultdict, deque
from typing import Dict, List, Optional, Any
import time


class OHLCCandle:
    """Represents an OHLC candle for a specific timeframe"""

    def __init__(self, timestamp: datetime, timeframe: str):
        self.timestamp = timestamp
        self.timeframe = timeframe
        self.open = None
        self.high = None
        self.low = None
        self.close = None
        self.volume = 0
        self.tick_count = 0
        self.is_complete = False

    def update_tick(self, price: float, volume: int = 1):
        """Update candle with new tick data"""
        if self.open is None:
            self.open = price

        if self.high is None or price > self.high:
            self.high = price

        if self.low is None or price < self.low:
            self.low = price

        self.close = price
        self.volume += volume
        self.tick_count += 1

    def complete(self):
        """Mark candle as complete"""
        self.is_complete = True
        if self.open is None:
            self.open = self.close
        if self.high is None:
            self.high = self.close
        if self.low is None:
            self.low = self.close

    def to_dict(self) -> Dict:
        """Convert candle to dictionary"""
        return {
            'timestamp': self.timestamp,
            'timeframe': self.timeframe,
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'tick_count': self.tick_count,
            'is_complete': self.is_complete
        }


class EMAStrategy:
    """
    EMA Crossover Strategy Implementation

    Processes real-time tick data, generates OHLC candles for multiple timeframes,
    calculates EMAs, detects crossover signals, and manages position tracking.
    """

    def __init__(self, ema_combinations: List[Dict], timeframes: List[str],
                 ema_calculator, signal_logger):
        """
        Initialize EMA strategy

        Args:
            ema_combinations: List of EMA combinations
            timeframes: List of timeframes to process
            ema_calculator: EMA calculator instance
            signal_logger: Signal logger instance
        """
        self.ema_combinations = ema_combinations
        self.timeframes = timeframes
        self.ema_calculator = ema_calculator
        self.signal_logger = signal_logger

        # Timeframe intervals in seconds
        self.timeframe_seconds = {
            '1min': 60,
            '5min': 300,
            '10min': 600,
            '15min': 900,
            '30min': 1800,
            '1hour': 3600
        }

        # Current candles for each timeframe
        self.current_candles = {}

        # Last candle timestamps
        self.last_candle_times = defaultdict(lambda: None)

        # Position tracking
        self.positions = defaultdict(lambda: defaultdict(dict))  # {timeframe: {ema_combo: position_info}}

        # Statistics
        self.stats = {
            'ticks_processed': 0,
            'candles_generated': defaultdict(int),
            'signals_generated': defaultdict(int),
            'last_update': None
        }

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"EMA Strategy initialized for timeframes: {timeframes}")

    def process_tick(self, tick_data: Dict):
        """
        Process incoming tick data

        Args:
            tick_data: Dictionary containing tick information
                      Expected keys: timestamp, price, volume (optional)
        """
        try:
            timestamp = tick_data.get('timestamp')
            price = tick_data.get('price')
            volume = tick_data.get('volume', 1)

            if not timestamp or not price:
                self.logger.warning(f"Invalid tick data: {tick_data}")
                return

            # Convert timestamp if needed
            if isinstance(timestamp, (int, float)):
                timestamp = datetime.fromtimestamp(timestamp)
            elif isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

            # Update statistics
            self.stats['ticks_processed'] += 1
            self.stats['last_update'] = timestamp

            # Process tick for each timeframe
            for timeframe in self.timeframes:
                self._process_tick_for_timeframe(timestamp, price, volume, timeframe)

        except Exception as e:
            self.logger.error(f"Error processing tick: {e}")

    def _process_tick_for_timeframe(self, timestamp: datetime, price: float,
                                  volume: int, timeframe: str):
        """Process tick for a specific timeframe"""
        try:
            # Get timeframe interval
            interval_seconds = self.timeframe_seconds.get(timeframe)
            if not interval_seconds:
                self.logger.warning(f"Unknown timeframe: {timeframe}")
                return

            # Calculate candle start time
            candle_start = self._get_candle_start_time(timestamp, interval_seconds)

            # Check if we need a new candle
            if (timeframe not in self.current_candles or
                self.current_candles[timeframe].timestamp != candle_start):

                # Complete previous candle if exists
                if timeframe in self.current_candles:
                    self._complete_candle(timeframe)

                # Create new candle
                self.current_candles[timeframe] = OHLCCandle(candle_start, timeframe)
                self.last_candle_times[timeframe] = candle_start

            # Update current candle
            self.current_candles[timeframe].update_tick(price, volume)

        except Exception as e:
            self.logger.error(f"Error processing tick for timeframe {timeframe}: {e}")

    def _get_candle_start_time(self, timestamp: datetime, interval_seconds: int) -> datetime:
        """Calculate the start time for a candle based on timestamp and interval"""
        # Round down to the nearest interval
        epoch = datetime(1970, 1, 1)
        seconds_since_epoch = (timestamp - epoch).total_seconds()
        candle_seconds = int(seconds_since_epoch // interval_seconds) * interval_seconds
        return datetime.fromtimestamp(candle_seconds)

    def _complete_candle(self, timeframe: str):
        """Complete a candle and process EMA calculations"""
        try:
            candle = self.current_candles[timeframe]
            candle.complete()

            # Update statistics
            self.stats['candles_generated'][timeframe] += 1

            # Use close price for EMA calculation
            close_price = candle.close
            if close_price is None:
                self.logger.warning(f"No close price for {timeframe} candle")
                return

            # Calculate EMAs
            emas = self.ema_calculator.add_price(timeframe, close_price)

            # Check for crossover signals
            signals = self.ema_calculator.get_crossover_signals(timeframe)

            # Process signals
            for signal in signals:
                self._process_signal(signal, candle)

            self.logger.debug(f"Completed {timeframe} candle: {candle.to_dict()}")

        except Exception as e:
            self.logger.error(f"Error completing candle for {timeframe}: {e}")

    def _process_signal(self, signal: Dict, candle: OHLCCandle):
        """Process a crossover signal"""
        try:
            timeframe = signal['timeframe']
            signal_type = signal['signal']
            short_ema = signal['short_ema']
            long_ema = signal['long_ema']
            price = signal['price']

            # Create position key
            ema_combo_key = f"{short_ema}_{long_ema}"

            # Update statistics
            self.stats['signals_generated'][f"{timeframe}_{signal_type}"] += 1

            # Get current position
            current_position = self.positions[timeframe][ema_combo_key]

            # Calculate P&L if closing position
            pnl = 0
            if current_position and 'entry_price' in current_position:
                if signal_type == 'SELL' and current_position.get('type') == 'BUY':
                    # Closing long position
                    pnl = price - current_position['entry_price']
                elif signal_type == 'BUY' and current_position.get('type') == 'SELL':
                    # Closing short position
                    pnl = current_position['entry_price'] - price

            # Update position
            if signal_type in ['BUY', 'SELL']:
                self.positions[timeframe][ema_combo_key] = {
                    'type': signal_type,
                    'entry_price': price,
                    'entry_time': candle.timestamp,
                    'short_ema': short_ema,
                    'long_ema': long_ema
                }

            # Prepare signal data for logging
            signal_data = {
                'datetime': candle.timestamp,
                'timeframe': timeframe,
                'action': signal_type,
                'price': price,
                'ema_combo': f"{short_ema}/{long_ema}",
                'short_ema_value': signal['short_value'],
                'long_ema_value': signal['long_value'],
                'entry_exit': 'ENTRY',  # For now, treating all as entries
                'pnl': pnl,
                'candle_data': candle.to_dict()
            }

            # Log the signal
            self.signal_logger.log_signal(signal_data)

            # Log to console
            self.logger.info(
                f"🔔 {signal_type} Signal: {timeframe} {ema_combo_key} @ {price:.2f} "
                f"(EMA{short_ema}: {signal['short_value']:.2f}, "
                f"EMA{long_ema}: {signal['long_value']:.2f}) P&L: {pnl:.2f}"
            )

        except Exception as e:
            self.logger.error(f"Error processing signal: {e}")

    def get_current_positions(self) -> Dict:
        """Get current positions for all timeframes and EMA combinations"""
        return dict(self.positions)

    def get_statistics(self) -> Dict:
        """Get strategy statistics"""
        return {
            'ticks_processed': self.stats['ticks_processed'],
            'candles_generated': dict(self.stats['candles_generated']),
            'signals_generated': dict(self.stats['signals_generated']),
            'last_update': self.stats['last_update'],
            'active_positions': len([
                pos for tf_positions in self.positions.values()
                for pos in tf_positions.values() if pos
            ]),
            'timeframes': self.timeframes,
            'ema_combinations': self.ema_combinations
        }

    def force_candle_completion(self):
        """Force completion of all current candles (useful for shutdown)"""
        for timeframe in list(self.current_candles.keys()):
            try:
                self._complete_candle(timeframe)
            except Exception as e:
                self.logger.error(f"Error force completing candle for {timeframe}: {e}")

    def reset(self):
        """Reset strategy state"""
        self.current_candles.clear()
        self.last_candle_times.clear()
        self.positions.clear()
        self.stats = {
            'ticks_processed': 0,
            'candles_generated': defaultdict(int),
            'signals_generated': defaultdict(int),
            'last_update': None
        }
        self.logger.info("Strategy state reset")