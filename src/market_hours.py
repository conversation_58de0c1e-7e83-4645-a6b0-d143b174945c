#!/usr/bin/env python3
"""
Market Hours Manager
===================

This module manages Indian stock market hours and trading sessions.
It handles market open/close times, trading days, and session state management.

Key Features:
- Indian market hours (9:15 AM - 3:15 PM IST)
- Trading day validation (Monday-Friday)
- Market holiday handling
- Session state persistence
- EMA state management across sessions

Author: AI Assistant
Date: 2025
"""

import os
import json
import pickle
import logging
from datetime import datetime, time, timedelta
from typing import Dict, Optional, Tuple
import pytz


class MarketHoursManager:
    """
    Manages Indian stock market hours and trading sessions
    """
    
    def __init__(self, config: Dict, data_directory: str = "data"):
        """
        Initialize market hours manager
        
        Args:
            config: Market hours configuration
            data_directory: Directory to store session state
        """
        self.config = config
        self.data_directory = data_directory
        
        # Market hours configuration
        self.timezone = pytz.timezone(config.get('timezone', 'Asia/Kolkata'))
        self.start_time = self._parse_time(config.get('start_time', '09:15'))
        self.end_time = self._parse_time(config.get('end_time', '15:15'))
        self.trading_days = config.get('trading_days', ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'])
        
        # Session state
        self.current_session_date = None
        self.session_state_file = os.path.join(data_directory, 'session_state.pkl')
        
        # Create data directory
        os.makedirs(data_directory, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Market Hours: {self.start_time.strftime('%H:%M')} - {self.end_time.strftime('%H:%M')} IST")
    
    def _parse_time(self, time_str: str) -> time:
        """Parse time string to time object"""
        try:
            return datetime.strptime(time_str, '%H:%M').time()
        except ValueError:
            self.logger.error(f"Invalid time format: {time_str}")
            return time(9, 15)  # Default to 9:15 AM
    
    def get_current_time(self) -> datetime:
        """Get current time in Indian timezone"""
        return datetime.now(self.timezone)
    
    def is_trading_day(self, date: datetime = None) -> bool:
        """Check if given date is a trading day"""
        if date is None:
            date = self.get_current_time()
        
        day_name = date.strftime('%A')
        return day_name in self.trading_days
    
    def is_market_open(self, current_time: datetime = None) -> bool:
        """Check if market is currently open"""
        if current_time is None:
            current_time = self.get_current_time()
        
        # Check if it's a trading day
        if not self.is_trading_day(current_time):
            return False
        
        # Check if current time is within market hours
        current_time_only = current_time.time()
        return self.start_time <= current_time_only <= self.end_time
    
    def get_market_session_info(self, current_time: datetime = None) -> Dict:
        """Get detailed market session information"""
        if current_time is None:
            current_time = self.get_current_time()
        
        is_trading_day = self.is_trading_day(current_time)
        is_open = self.is_market_open(current_time)
        
        # Calculate next market open/close times
        next_open, next_close = self._get_next_market_times(current_time)
        
        return {
            'current_time': current_time,
            'is_trading_day': is_trading_day,
            'is_market_open': is_open,
            'market_start': self.start_time,
            'market_end': self.end_time,
            'next_open': next_open,
            'next_close': next_close,
            'session_date': current_time.date().isoformat()
        }
    
    def _get_next_market_times(self, current_time: datetime) -> Tuple[datetime, datetime]:
        """Calculate next market open and close times"""
        current_date = current_time.date()
        
        # Today's market times
        today_open = self.timezone.localize(datetime.combine(current_date, self.start_time))
        today_close = self.timezone.localize(datetime.combine(current_date, self.end_time))
        
        # If market hasn't opened today and it's a trading day
        if current_time < today_open and self.is_trading_day(current_time):
            return today_open, today_close
        
        # Find next trading day
        next_date = current_date + timedelta(days=1)
        while not self.is_trading_day(self.timezone.localize(datetime.combine(next_date, time(12, 0)))):
            next_date += timedelta(days=1)
        
        next_open = self.timezone.localize(datetime.combine(next_date, self.start_time))
        next_close = self.timezone.localize(datetime.combine(next_date, self.end_time))
        
        return next_open, next_close
    
    def is_new_trading_session(self, current_time: datetime = None) -> bool:
        """Check if this is a new trading session"""
        if current_time is None:
            current_time = self.get_current_time()
        
        session_date = current_time.date().isoformat()
        
        if self.current_session_date != session_date:
            self.current_session_date = session_date
            return True
        
        return False
    
    def should_reset_ema_state(self, current_time: datetime = None) -> bool:
        """Check if EMA state should be reset for new trading day"""
        if current_time is None:
            current_time = self.get_current_time()
        
        # Reset EMA state at market open of new trading day
        if not self.is_trading_day(current_time):
            return False
        
        # Check if we're at or after market open and it's a new session
        current_time_only = current_time.time()
        if current_time_only >= self.start_time and self.is_new_trading_session(current_time):
            return True
        
        return False
    
    def save_session_state(self, state_data: Dict):
        """Save session state to file"""
        try:
            state_data['timestamp'] = self.get_current_time().isoformat()
            state_data['session_date'] = self.current_session_date
            
            with open(self.session_state_file, 'wb') as f:
                pickle.dump(state_data, f)
            
            self.logger.debug(f"Session state saved for {self.current_session_date}")
            
        except Exception as e:
            self.logger.error(f"Error saving session state: {e}")
    
    def load_session_state(self) -> Optional[Dict]:
        """Load session state from file"""
        try:
            if not os.path.exists(self.session_state_file):
                return None
            
            with open(self.session_state_file, 'rb') as f:
                state_data = pickle.load(f)
            
            # Check if state is from current session
            if state_data.get('session_date') == self.current_session_date:
                self.logger.info(f"Loaded session state for {self.current_session_date}")
                return state_data
            else:
                self.logger.info(f"Session state is from different day, starting fresh")
                return None
                
        except Exception as e:
            self.logger.error(f"Error loading session state: {e}")
            return None
    
    def wait_for_market_open(self, check_interval: int = 60):
        """Wait for market to open (blocking)"""
        import time
        
        while True:
            current_time = self.get_current_time()
            session_info = self.get_market_session_info(current_time)
            
            if session_info['is_market_open']:
                self.logger.info("🔔 Market is now OPEN!")
                break
            
            next_open = session_info['next_open']
            time_to_open = (next_open - current_time).total_seconds()
            
            if time_to_open > 0:
                hours = int(time_to_open // 3600)
                minutes = int((time_to_open % 3600) // 60)
                
                self.logger.info(f"⏰ Market closed. Next open in {hours}h {minutes}m at {next_open.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Sleep for check_interval or until market opens, whichever is shorter
                sleep_time = min(check_interval, time_to_open)
                time.sleep(sleep_time)
            else:
                break
    
    def get_time_until_market_close(self, current_time: datetime = None) -> Optional[int]:
        """Get seconds until market close (None if market is closed)"""
        if current_time is None:
            current_time = self.get_current_time()
        
        if not self.is_market_open(current_time):
            return None
        
        # Calculate time until market close
        current_date = current_time.date()
        market_close = self.timezone.localize(datetime.combine(current_date, self.end_time))
        
        time_to_close = (market_close - current_time).total_seconds()
        return max(0, int(time_to_close))
    
    def get_market_status_string(self, current_time: datetime = None) -> str:
        """Get human-readable market status"""
        session_info = self.get_market_session_info(current_time)
        
        if session_info['is_market_open']:
            time_to_close = self.get_time_until_market_close(session_info['current_time'])
            if time_to_close:
                hours = time_to_close // 3600
                minutes = (time_to_close % 3600) // 60
                return f"🟢 MARKET OPEN - Closes in {hours}h {minutes}m"
            else:
                return "🟢 MARKET OPEN"
        else:
            if session_info['is_trading_day']:
                return "🔴 MARKET CLOSED - Trading day"
            else:
                return "🔴 MARKET CLOSED - Non-trading day"
