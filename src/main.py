#!/usr/bin/env python3
"""
Real-time EMA Crossover Logging System for NIFTY 50
===================================================

This is the main entry point for the EMA crossover trading system that:
1. Connects to DhanHQ WebSocket for live NIFTY 50 data
2. Generates synthetic OHLC candles for multiple timeframes
3. Calculates EMAs and detects crossover signals
4. Logs all signals to CSV files with P&L tracking

Usage:
    python src/main.py

Requirements:
    - DhanHQ account with API access
    - Valid client_id and access_token in config/config.json
    - Internet connection for live data feed

Author: AI Assistant
Date: 2025
"""

import os
import sys
import json
import logging
import signal
import time
from datetime import datetime
from pathlib import Path

# Add src directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.market_feed import DhanMarketFeed, MockMarketFeed
from core.strategy import EMAStrategy
from data.logger import SignalLogger
from core.ema import EMACalculator
from utils.market_hours import MarketHoursManager
from utils.state_manager import StateManager
from data.historical_data import HistoricalDataRecovery
from data.historical_database import HistoricalDatabase


class EMATradeSystem:
    """Main trading system orchestrator"""

    def __init__(self, config_path="config/config.json"):
        """Initialize the trading system with configuration"""
        self.config = self.load_config(config_path)
        self.setup_logging()
        self.setup_directories()

        # Initialize components
        self.market_hours_manager = None
        self.historical_database = None
        self.historical_recovery = None
        self.state_manager = None
        self.market_feed = None
        self.strategy = None
        self.signal_logger = None
        self.ema_calculator = None

        # System state
        self.running = False
        self.background_mode = False
        self.setup_signal_handlers()

        self.logger.info("EMA Trading System initialized")

    def load_config(self, config_path):
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)

            # Validate required fields
            required_fields = ['dhan_credentials', 'instrument', 'ema_combinations', 'timeframes']
            for field in required_fields:
                if field not in config:
                    raise ValueError(f"Missing required config field: {field}")

            return config
        except Exception as e:
            print(f"Error loading config: {e}")
            sys.exit(1)

    def setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config.get('logging', {}).get('log_level', 'INFO'))

        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)

        # Setup logging format
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        # Configure logging
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(f'logs/ema_system_{datetime.now().strftime("%Y%m%d")}.log'),
                logging.StreamHandler() if self.config.get('logging', {}).get('console_output', True) else logging.NullHandler()
            ]
        )

        self.logger = logging.getLogger(__name__)

    def setup_directories(self):
        """Create necessary directories"""
        data_dir = self.config.get('data_directory', 'data')
        os.makedirs(data_dir, exist_ok=True)
        self.logger.info(f"Data directory: {data_dir}")

    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()

    def validate_credentials(self):
        """Validate DhanHQ credentials"""
        creds = self.config['dhan_credentials']

        if creds['client_id'] == "YOUR_CLIENT_ID_HERE" or creds['access_token'] == "YOUR_ACCESS_TOKEN_HERE":
            self.logger.warning("DhanHQ credentials not configured - will use Mock Market Feed for testing")
            self.logger.info("To use live data, update your credentials in config/config.json")
            self.logger.info("Get your credentials from: https://web.dhan.co -> My Profile -> Access DhanHQ APIs")
            return True  # Allow mock mode

        return True

    def initialize_components(self):
        """Initialize all system components"""
        try:
            # Initialize market hours manager
            self.market_hours_manager = MarketHoursManager(
                config=self.config.get('market_hours', {}),
                data_directory=self.config['data_directory']
            )

            # Initialize historical database (2-week rolling data)
            self.historical_database = HistoricalDatabase(
                dhan_credentials=self.config['dhan_credentials'],
                market_hours_config=self.config.get('market_hours', {}),
                data_directory=self.config['data_directory']
            )

            # Initialize historical data recovery
            self.historical_recovery = HistoricalDataRecovery(
                dhan_credentials=self.config['dhan_credentials'],
                market_hours_config=self.config.get('market_hours', {})
            )

            # Initialize state manager for EMA persistence
            self.state_manager = StateManager(
                data_directory=self.config['data_directory']
            )

            # Initialize EMA calculator
            self.ema_calculator = EMACalculator(self.config['ema_combinations'])

            # Initialize signal logger (simplified for daily CSV)
            self.signal_logger = SignalLogger(
                data_directory=self.config['data_directory'],
                initial_capital=self.config.get('trading', {}).get('initial_capital', 100000)
            )

            # Initialize strategy with state manager
            self.strategy = EMAStrategy(
                ema_combinations=self.config['ema_combinations'],
                timeframes=self.config['timeframes'],
                ema_calculator=self.ema_calculator,
                signal_logger=self.signal_logger,
                state_manager=self.state_manager
            )

            # Initialize market feed (use mock feed if credentials are not set)
            use_mock = (self.config['dhan_credentials']['client_id'] == "YOUR_CLIENT_ID_HERE" or
                       self.config['dhan_credentials']['access_token'] == "YOUR_ACCESS_TOKEN_HERE")

            if use_mock:
                self.logger.info("Using Mock Market Feed for testing")
                self.market_feed = MockMarketFeed(
                    instrument=self.config['instrument'],
                    strategy=self.strategy
                )
            else:
                self.logger.info("Using Live DhanHQ Market Feed")
                self.market_feed = DhanMarketFeed(
                    client_id=self.config['dhan_credentials']['client_id'],
                    access_token=self.config['dhan_credentials']['access_token'],
                    instrument=self.config['instrument'],
                    strategy=self.strategy
                )

            self.logger.info("All components initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing components: {e}")
            return False

    def start(self, background_mode=False):
        """Start the trading system with market hours awareness"""
        self.background_mode = background_mode
        self.logger.info("Starting EMA Trading System...")

        # Validate credentials
        if not self.validate_credentials():
            return False

        # Initialize components
        if not self.initialize_components():
            return False

        # Check market hours and handle accordingly
        session_info = self.market_hours_manager.get_market_session_info()
        self.logger.info(f"Market Status: {self.market_hours_manager.get_market_status_string()}")

        # Update historical database before trading starts
        self.logger.info("📊 Checking historical database...")
        if self.historical_database.is_update_needed():
            self.logger.info("🔄 Updating 2-week historical database...")
            update_success = self.historical_database.update_historical_data(
                security_id=self.config['instrument']['security_id'],
                exchange_segment=self.config['instrument']['exchange_segment']
            )

            if update_success:
                db_info = self.historical_database.get_database_info()
                self.logger.info(f"✅ Historical database updated: {db_info['total_days']} days, "
                               f"{db_info['total_candles']} candles")
            else:
                self.logger.warning("⚠️  Historical database update failed, continuing with existing data")
        else:
            db_info = self.historical_database.get_database_info()
            self.logger.info(f"📊 Historical database current: {db_info['total_days']} days, "
                           f"{db_info['total_candles']} candles")

        # Initialize EMAs with historical data
        self.logger.info("🧮 Initializing EMAs with historical data...")
        historical_prices = self.historical_database.get_historical_prices(days=10)  # Last 10 days

        if historical_prices:
            self.ema_calculator.load_state_from_prices("1min", historical_prices)
            self.logger.info(f"✅ EMAs initialized with {len(historical_prices)} historical prices")

            # Show current EMA values
            current_emas = self.ema_calculator.get_current_ema_values("1min")
            if current_emas:
                ema_info = [f"{k}={v:.2f}" for k, v in current_emas.items()]
                self.logger.info(f"📈 Current EMAs: {', '.join(ema_info)}")
        else:
            self.logger.warning("⚠️  No historical prices available for EMA initialization")

        # Handle EMA state based on trading session
        if self.market_hours_manager.should_reset_ema_state():
            self.logger.info("🔄 New trading day detected - but EMAs already initialized with historical data")
            self.signal_logger.reset_daily_state()
            self.state_manager.reset_daily_state()
        else:
            # Load existing state for current trading day
            self.logger.info("📂 Loading existing EMA state for current trading day")
            self.strategy.load_historical_state()

        # Recover historical crossovers if needed
        if self.historical_recovery.is_recovery_needed(self.signal_logger):
            self.logger.info("🔍 Recovering historical EMA crossovers from market open...")
            crossovers_found = self.historical_recovery.recover_historical_crossovers(
                security_id=self.config['instrument']['security_id'],
                exchange_segment=self.config['instrument']['exchange_segment'],
                ema_calculator=self.ema_calculator,
                signal_logger=self.signal_logger,
                strategy=self.strategy
            )

            if crossovers_found > 0:
                self.logger.info(f"✅ Recovered {crossovers_found} historical crossovers")
                # Save updated state
                self.state_manager.save_daily_state()
            else:
                self.logger.info("ℹ️  No historical crossovers found")

        # Start market feed
        try:
            self.running = True

            if background_mode:
                self.logger.info("🚀 System started in BACKGROUND mode")
                self.logger.info("System will wait for market hours and run continuously")
                return self._run_background_mode()
            else:
                self.logger.info("🚀 System started in FOREGROUND mode. Press Ctrl+C to stop.")

                # Check if market is open
                if not session_info['is_market_open']:
                    self.logger.info("⏰ Market is currently closed")
                    if input("Wait for market to open? (y/n): ").lower() == 'y':
                        self.market_hours_manager.wait_for_market_open()
                    else:
                        self.logger.info("Starting with mock data for testing...")

                # Start the market feed
                self.market_feed.start()

        except Exception as e:
            self.logger.error(f"Error starting system: {e}")
            return False

        return True

    def _run_background_mode(self):
        """Run system in background mode with market hours awareness"""
        import time

        while self.running:
            try:
                # Check market status
                session_info = self.market_hours_manager.get_market_session_info()

                if session_info['is_market_open']:
                    # Market is open - ensure feed is running
                    if not self.market_feed.is_connected():
                        self.logger.info("🔔 Market opened - starting data feed")
                        self.market_feed.start()

                    # Check if it's a new trading day
                    if self.market_hours_manager.is_new_trading_session():
                        self.logger.info("🔄 New trading session - resetting EMA state")
                        self.ema_calculator.reset_all()
                        self.signal_logger.reset_daily_state()

                    # Sleep for 1 minute during market hours
                    time.sleep(60)

                else:
                    # Market is closed - stop feed and wait
                    if self.market_feed.is_connected():
                        self.logger.info("🔴 Market closed - stopping data feed")
                        self.market_feed.stop()

                        # Generate daily summary
                        summary = self.signal_logger.create_summary_report()
                        self.logger.info(f"\n{summary}")

                    # Wait for next market open
                    self.logger.info("⏰ Waiting for next market session...")
                    self.market_hours_manager.wait_for_market_open(check_interval=300)  # Check every 5 minutes

            except KeyboardInterrupt:
                self.logger.info("Background mode interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Error in background mode: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

        return True

    def stop(self):
        """Stop the trading system gracefully"""
        if not self.running:
            return

        self.logger.info("Stopping EMA Trading System...")
        self.running = False

        # Stop market feed
        if self.market_feed:
            self.market_feed.stop()

        # Save current state before shutdown
        if self.state_manager:
            try:
                self.state_manager.save_daily_state()
                self.logger.info("💾 EMA state saved for session continuity")
            except Exception as e:
                self.logger.error(f"Error saving state: {e}")

        # Close signal logger
        if self.signal_logger:
            self.signal_logger.close()

        self.logger.info("System stopped successfully")

    def run(self):
        """Main run method"""
        try:
            if self.start():
                # Keep the main thread alive
                while self.running:
                    time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Keyboard interrupt received")
        finally:
            self.stop()


def main():
    """Main entry point"""
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='NIFTY 50 EMA Crossover Trading System')
    parser.add_argument('--background', '-b', action='store_true',
                       help='Run in background mode (daemon-like)')
    parser.add_argument('--config', '-c', default='config/config.json',
                       help='Path to configuration file')

    args = parser.parse_args()

    print("=" * 60)
    print("Real-time EMA Crossover Logging System for NIFTY 50")
    print("=" * 60)
    print(f"Mode: {'BACKGROUND' if args.background else 'FOREGROUND'}")
    print(f"Config: {args.config}")
    print()

    # Check if config file exists
    if not os.path.exists(args.config):
        print(f"❌ Config file not found: {args.config}")
        print("Please create the config file with your DhanHQ credentials.")
        sys.exit(1)

    try:
        # Create and run the trading system
        system = EMATradeSystem(args.config)

        if args.background:
            # Run in background mode
            system.start(background_mode=True)
        else:
            # Run in foreground mode
            system.run()

    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()