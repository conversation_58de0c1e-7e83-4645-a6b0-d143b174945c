#!/usr/bin/env python3
"""
Signal Logger Module
===================

This module handles CSV logging of EMA crossover signals with P&L tracking.
It creates separate CSV files for each timeframe and maintains running totals
of profits and losses for performance analysis.

Key Features:
- Separate CSV files per timeframe
- Comprehensive signal logging with OHLC data
- P&L tracking and cumulative calculations
- Thread-safe file operations
- Automatic file rotation and backup
- Performance statistics

CSV Columns:
- Datetime: Signal timestamp
- Action: BUY/SELL signal type
- Price: Signal price
- EMA Combo: EMA combination (e.g., "5/10")
- Entry/Exit: Position entry or exit
- P&L: Profit/Loss for the signal
- Short EMA: Short EMA value
- Long EMA: Long EMA value
- Candle Open/High/Low/Close: OHLC data
- Volume: Candle volume
- Cumulative P&L: Running total

Author: AI Assistant
Date: 2025
"""

import os
import csv
import logging
import threading
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path


class SignalLogger:
    """
    CSV Signal Logger with P&L Tracking

    Logs EMA crossover signals to separate CSV files per timeframe.
    Maintains running P&L calculations and provides performance statistics.
    """

    def __init__(self, data_directory: str, initial_capital: float = 100000):
        """
        Initialize signal logger with single daily CSV file

        Args:
            data_directory: Directory to store CSV files
            initial_capital: Initial capital for P&L calculations
        """
        self.data_directory = Path(data_directory)
        self.initial_capital = initial_capital

        # Create data directory
        self.data_directory.mkdir(exist_ok=True)

        # Single CSV file for the day
        self.csv_file = None
        self.csv_writer = None
        self.current_date = None

        # P&L tracking
        self.cumulative_pnl = 0.0
        self.trade_count = 0
        self.daily_signals = []

        # Thread lock for file operations
        self.lock = threading.Lock()

        # CSV headers
        self.csv_headers = [
            'Date',
            'Time',
            'Action',
            'Price',
            'EMA5_Value',
            'EMA10_Value',
            'PnL',
            'Cumulative_PnL',
            'Signal_Number'
        ]

        self.logger = logging.getLogger(__name__)

        # Initialize CSV file for today
        self._initialize_daily_csv()

        self.logger.info(f"Signal Logger initialized with daily CSV file")

    def _initialize_daily_csv(self):
        """Initialize or open daily CSV file"""
        try:
            today = datetime.now().strftime("%Y%m%d")

            # Check if we need to create a new file for today
            if self.current_date != today:
                # Close previous file if open
                if self.csv_file:
                    self.csv_file.close()

                # Create filename for today
                filename = f"nifty50_ema_signals_{today}.csv"
                filepath = self.data_directory / filename

                # Check if file exists (append mode) or create new
                file_exists = filepath.exists()

                # Open file in append mode
                self.csv_file = open(filepath, 'a', newline='', encoding='utf-8')
                self.csv_writer = csv.DictWriter(self.csv_file, fieldnames=self.csv_headers)

                # Write header only if file is new
                if not file_exists:
                    self.csv_writer.writeheader()
                    self.csv_file.flush()
                    self.logger.info(f"Created new daily CSV file: {filepath}")
                else:
                    # Load existing data to continue P&L tracking
                    self._load_daily_state(filepath)
                    self.logger.info(f"Opened existing daily CSV file: {filepath}")

                self.current_date = today

        except Exception as e:
            self.logger.error(f"Error initializing daily CSV: {e}")

    def _load_daily_state(self, filepath):
        """Load existing daily state from CSV file"""
        try:
            # Read existing CSV to get current state (without pandas)
            if filepath.exists():
                with open(filepath, 'r') as f:
                    lines = f.readlines()
                    if len(lines) > 1:  # Has data beyond header
                        # Count lines (excluding header)
                        self.trade_count = len(lines) - 1

                        # Parse all existing signals
                        self.daily_signals = []
                        for line in lines[1:]:  # Skip header
                            line = line.strip()
                            if line:
                                fields = line.split(',')
                                if len(fields) >= 8:
                                    signal_record = {
                                        'Date': fields[0],
                                        'Time': fields[1],
                                        'Action': fields[2],
                                        'Price': fields[3],
                                        'EMA5_Value': fields[4],
                                        'EMA10_Value': fields[5],
                                        'PnL': fields[6],
                                        'Cumulative_PnL': fields[7],
                                        'Signal_Number': fields[8] if len(fields) > 8 else str(len(self.daily_signals) + 1)
                                    }
                                    self.daily_signals.append(signal_record)

                        # Get last cumulative P&L
                        if self.daily_signals:
                            try:
                                self.cumulative_pnl = float(self.daily_signals[-1]['Cumulative_PnL'])
                            except (ValueError, KeyError):
                                self.cumulative_pnl = 0.0

                        self.logger.info(f"Loaded daily state: {self.trade_count} signals, P&L: {self.cumulative_pnl:.2f}")

        except Exception as e:
            self.logger.warning(f"Could not load daily state: {e}")
            # Reset to defaults
            self.trade_count = 0
            self.cumulative_pnl = 0.0
            self.daily_signals = []

    def get_historical_prices_from_signals(self) -> List[float]:
        """Extract historical prices from existing signals for EMA reconstruction"""
        prices = []
        try:
            for signal in self.daily_signals:
                try:
                    price = float(signal['Price'])
                    prices.append(price)
                except (ValueError, KeyError):
                    continue

            if prices:
                self.logger.info(f"Extracted {len(prices)} historical prices from existing signals")

        except Exception as e:
            self.logger.error(f"Error extracting historical prices: {e}")

        return prices

    def log_signal(self, signal_data: Dict):
        """
        Log a signal to the daily CSV file

        Args:
            signal_data: Dictionary containing signal information
        """
        try:
            # Ensure we have the correct daily CSV file
            self._initialize_daily_csv()

            with self.lock:
                # Calculate P&L for this signal
                pnl = signal_data.get('pnl', 0)
                self.cumulative_pnl += pnl
                self.trade_count += 1

                # Get signal timestamp
                signal_datetime = signal_data.get('datetime', datetime.now())

                # Prepare CSV row
                csv_row = {
                    'Date': signal_datetime.strftime('%Y-%m-%d'),
                    'Time': signal_datetime.strftime('%H:%M:%S'),
                    'Action': signal_data.get('action', ''),
                    'Price': f"{signal_data.get('price', 0):.2f}",
                    'EMA5_Value': f"{signal_data.get('short_ema_value', 0):.2f}",
                    'EMA10_Value': f"{signal_data.get('long_ema_value', 0):.2f}",
                    'PnL': f"{pnl:.2f}",
                    'Cumulative_PnL': f"{self.cumulative_pnl:.2f}",
                    'Signal_Number': self.trade_count
                }

                # Write to CSV
                if self.csv_writer:
                    self.csv_writer.writerow(csv_row)
                    self.csv_file.flush()

                    # Add to daily signals list
                    self.daily_signals.append(csv_row)

                    self.logger.info(f"📝 Signal #{self.trade_count} logged: {csv_row['Action']} @ {csv_row['Price']}")

        except Exception as e:
            self.logger.error(f"Error logging signal: {e}")

    def get_statistics(self) -> Dict:
        """
        Get logging statistics

        Returns:
            Dictionary with daily statistics
        """
        return {
            'total_signals': self.trade_count,
            'cumulative_pnl': self.cumulative_pnl,
            'average_pnl': (self.cumulative_pnl / self.trade_count if self.trade_count > 0 else 0),
            'current_date': self.current_date,
            'csv_file': str(self.data_directory / f"nifty50_ema_signals_{self.current_date}.csv") if self.current_date else None,
            'daily_signals': len(self.daily_signals)
        }

    def close(self):
        """Close daily CSV file"""
        with self.lock:
            if self.csv_file:
                try:
                    self.csv_file.close()
                    self.logger.info(f"Closed daily CSV file for {self.current_date}")
                except Exception as e:
                    self.logger.error(f"Error closing daily CSV file: {e}")
                finally:
                    self.csv_file = None
                    self.csv_writer = None

    def create_summary_report(self) -> str:
        """
        Create a summary report of daily signals

        Returns:
            String containing formatted summary report
        """
        report = []
        report.append("=" * 60)
        report.append("NIFTY 50 EMA CROSSOVER DAILY SUMMARY")
        report.append("=" * 60)
        report.append(f"Date: {self.current_date}")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Initial Capital: ₹{self.initial_capital:,.2f}")
        report.append("")

        report.append(f"Daily Performance:")
        report.append(f"  Total Signals: {self.trade_count}")
        report.append(f"  Total P&L: ₹{self.cumulative_pnl:,.2f}")
        report.append(f"  Average P&L per Signal: ₹{(self.cumulative_pnl / self.trade_count if self.trade_count > 0 else 0):,.2f}")
        report.append(f"  Return %: {(self.cumulative_pnl / self.initial_capital * 100):.2f}%")
        report.append("")

        if self.daily_signals:
            report.append("Recent Signals:")
            report.append("-" * 40)
            for signal in self.daily_signals[-5:]:  # Last 5 signals
                report.append(f"  {signal['Time']} - {signal['Action']} @ ₹{signal['Price']} (P&L: ₹{signal['PnL']})")

        report.append("")
        report.append("CSV File:")
        report.append(f"  {self.data_directory / f'nifty50_ema_signals_{self.current_date}.csv'}")
        report.append("=" * 60)

        return "\n".join(report)

    def get_daily_signals(self) -> list:
        """Get all signals for the current day"""
        return self.daily_signals.copy()

    def reset_daily_state(self):
        """Reset daily state for new trading day"""
        with self.lock:
            self.cumulative_pnl = 0.0
            self.trade_count = 0
            self.daily_signals = []
            self.logger.info("Daily state reset for new trading session")