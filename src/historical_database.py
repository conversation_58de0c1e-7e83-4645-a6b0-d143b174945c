#!/usr/bin/env python3
"""
Historical Database Manager
===========================

This module manages a rolling 2-week historical database of NIFTY 50 data.
It fetches, stores, and maintains historical candle data to provide better
EMA initialization and more accurate crossover detection.

Key Features:
- Maintain rolling 2-week historical database
- Daily data updates before market open
- Separate storage for historical vs live data
- Efficient data retrieval and management
- Automatic cleanup of old data

Author: AI Assistant
Date: 2025
"""

import os
import json
import pickle
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import pytz


class HistoricalDatabase:
    """
    Manages historical NIFTY 50 data for EMA calculations
    """
    
    def __init__(self, dhan_credentials: Dict, market_hours_config: Dict, 
                 data_directory: str = "data"):
        """
        Initialize historical database manager
        
        Args:
            dhan_credentials: DhanHQ API credentials
            market_hours_config: Market hours configuration
            data_directory: Base data directory
        """
        self.client_id = dhan_credentials.get('client_id')
        self.access_token = dhan_credentials.get('access_token')
        self.timezone = pytz.timezone(market_hours_config.get('timezone', 'Asia/Kolkata'))
        self.market_start = market_hours_config.get('start_time', '09:15')
        self.market_end = market_hours_config.get('end_time', '15:15')
        
        # Directory structure
        self.base_dir = Path(data_directory)
        self.historical_dir = self.base_dir / "historical"
        self.historical_dir.mkdir(parents=True, exist_ok=True)
        
        # Database files
        self.db_file = self.historical_dir / "nifty50_historical.pkl"
        self.metadata_file = self.historical_dir / "metadata.json"
        
        self.logger = logging.getLogger(__name__)
        
        # DhanHQ API configuration
        self.base_url = "https://api.dhan.co"
        self.headers = {
            'Content-Type': 'application/json',
            'access-token': self.access_token
        }
        
        # Data retention (14 trading days)
        self.retention_days = 14
        
        self.logger.info(f"Historical Database initialized: {self.historical_dir}")
    
    def get_trading_days_range(self, end_date: datetime = None) -> List[datetime]:
        """
        Get list of trading days for the past 2 weeks
        
        Args:
            end_date: End date (defaults to today)
            
        Returns:
            List of trading day dates
        """
        if end_date is None:
            end_date = datetime.now(self.timezone).date()
        
        trading_days = []
        current_date = end_date - timedelta(days=self.retention_days + 5)  # Extra buffer
        
        while len(trading_days) < self.retention_days and current_date <= end_date:
            # Check if it's a weekday (Monday=0, Sunday=6)
            if current_date.weekday() < 5:  # Monday to Friday
                trading_days.append(current_date)
            current_date += timedelta(days=1)
        
        # Keep only the last retention_days
        return trading_days[-self.retention_days:]
    
    def fetch_daily_data(self, date: datetime, security_id: str, 
                        exchange_segment: str) -> List[Dict]:
        """
        Fetch complete day's data for a specific date
        
        Args:
            date: Date to fetch data for
            security_id: Security ID
            exchange_segment: Exchange segment
            
        Returns:
            List of candle dictionaries for the day
        """
        try:
            # Create market start and end times for the date
            market_start = self.timezone.localize(
                datetime.combine(date, datetime.strptime(self.market_start, '%H:%M').time())
            )
            market_end = self.timezone.localize(
                datetime.combine(date, datetime.strptime(self.market_end, '%H:%M').time())
            )
            
            self.logger.info(f"Fetching data for {date.strftime('%Y-%m-%d')}")
            
            # DhanHQ historical data API
            url = f"{self.base_url}/charts/historical"
            
            payload = {
                "securityId": security_id,
                "exchangeSegment": exchange_segment,
                "instrument": "INDEX",
                "fromDate": int(market_start.timestamp()),
                "toDate": int(market_end.timestamp()),
                "resolution": "1"  # 1-minute resolution
            }
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('status') == 'success':
                    candles_data = data.get('data', {})
                    
                    # Extract OHLC data
                    timestamps = candles_data.get('t', [])
                    opens = candles_data.get('o', [])
                    highs = candles_data.get('h', [])
                    lows = candles_data.get('l', [])
                    closes = candles_data.get('c', [])
                    volumes = candles_data.get('v', [])
                    
                    candles = []
                    for i in range(len(timestamps)):
                        candle_time = datetime.fromtimestamp(timestamps[i], tz=self.timezone)
                        
                        candle = {
                            'timestamp': candle_time,
                            'date': date.strftime('%Y-%m-%d'),
                            'open': float(opens[i]),
                            'high': float(highs[i]),
                            'low': float(lows[i]),
                            'close': float(closes[i]),
                            'volume': int(volumes[i]) if i < len(volumes) else 0
                        }
                        candles.append(candle)
                    
                    self.logger.info(f"Fetched {len(candles)} candles for {date.strftime('%Y-%m-%d')}")
                    return candles
                else:
                    self.logger.error(f"API error for {date}: {data.get('message', 'Unknown error')}")
                    return []
            else:
                self.logger.error(f"HTTP error {response.status_code} for {date}: {response.text}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error fetching data for {date}: {e}")
            return []
    
    def generate_mock_daily_data(self, date: datetime, base_price: float = 24750.0) -> List[Dict]:
        """
        Generate mock daily data for testing
        
        Args:
            date: Date to generate data for
            base_price: Base price for simulation
            
        Returns:
            List of mock candle dictionaries
        """
        import random
        
        candles = []
        
        # Market hours for the date
        market_start = datetime.combine(date, datetime.strptime(self.market_start, '%H:%M').time())
        market_end = datetime.combine(date, datetime.strptime(self.market_end, '%H:%M').time())
        
        current_time = market_start
        current_price = base_price + random.uniform(-100, 100)  # Daily variation
        
        self.logger.info(f"Generating mock data for {date.strftime('%Y-%m-%d')}")
        
        while current_time < market_end:
            # Generate realistic price movement
            change_percent = random.uniform(-0.002, 0.002)  # ±0.2% change
            price_change = current_price * change_percent
            
            open_price = current_price
            close_price = current_price + price_change
            
            # Generate high/low around open/close
            high_price = max(open_price, close_price) + abs(price_change) * random.uniform(0, 0.5)
            low_price = min(open_price, close_price) - abs(price_change) * random.uniform(0, 0.5)
            
            candle = {
                'timestamp': self.timezone.localize(current_time),
                'date': date.strftime('%Y-%m-%d'),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': random.randint(1000, 5000)
            }
            
            candles.append(candle)
            current_price = close_price
            current_time += timedelta(minutes=1)
        
        self.logger.info(f"Generated {len(candles)} mock candles for {date.strftime('%Y-%m-%d')}")
        return candles
    
    def load_database(self) -> Dict:
        """
        Load existing historical database
        
        Returns:
            Dictionary with historical data
        """
        try:
            if self.db_file.exists():
                with open(self.db_file, 'rb') as f:
                    database = pickle.load(f)
                
                self.logger.info(f"Loaded historical database with {len(database)} days")
                return database
            else:
                self.logger.info("No existing database found, starting fresh")
                return {}
                
        except Exception as e:
            self.logger.error(f"Error loading database: {e}")
            return {}
    
    def save_database(self, database: Dict):
        """
        Save historical database to file
        
        Args:
            database: Database dictionary to save
        """
        try:
            with open(self.db_file, 'wb') as f:
                pickle.dump(database, f)
            
            # Update metadata
            metadata = {
                'last_updated': datetime.now(self.timezone).isoformat(),
                'total_days': len(database),
                'date_range': {
                    'start': min(database.keys()) if database else None,
                    'end': max(database.keys()) if database else None
                }
            }
            
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            self.logger.info(f"Saved database with {len(database)} days")
            
        except Exception as e:
            self.logger.error(f"Error saving database: {e}")
    
    def update_historical_data(self, security_id: str, exchange_segment: str, 
                             force_update: bool = False) -> bool:
        """
        Update historical database with latest data
        
        Args:
            security_id: Security ID
            exchange_segment: Exchange segment
            force_update: Force update even if data exists
            
        Returns:
            True if update was successful
        """
        try:
            self.logger.info("Starting historical data update...")
            
            # Load existing database
            database = self.load_database()
            
            # Get trading days to fetch
            trading_days = self.get_trading_days_range()
            
            updated_days = 0
            
            for date in trading_days:
                date_str = date.strftime('%Y-%m-%d')
                
                # Skip if data already exists and not forcing update
                if date_str in database and not force_update:
                    self.logger.debug(f"Data for {date_str} already exists, skipping")
                    continue
                
                # Fetch data for this date
                daily_data = self.fetch_daily_data(date, security_id, exchange_segment)
                
                # Fallback to mock data if API fails
                if not daily_data:
                    self.logger.warning(f"API failed for {date_str}, generating mock data")
                    daily_data = self.generate_mock_daily_data(date)
                
                if daily_data:
                    database[date_str] = daily_data
                    updated_days += 1
                    self.logger.info(f"Updated data for {date_str}: {len(daily_data)} candles")
                
                # Small delay to avoid API rate limits
                import time
                time.sleep(0.5)
            
            # Clean up old data
            self._cleanup_old_data(database)
            
            # Save updated database
            self.save_database(database)
            
            self.logger.info(f"Historical data update complete: {updated_days} days updated")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating historical data: {e}")
            return False
    
    def _cleanup_old_data(self, database: Dict):
        """
        Remove data older than retention period
        
        Args:
            database: Database dictionary to clean
        """
        try:
            cutoff_date = (datetime.now(self.timezone).date() - 
                          timedelta(days=self.retention_days + 5)).strftime('%Y-%m-%d')
            
            old_dates = [date for date in database.keys() if date < cutoff_date]
            
            for date in old_dates:
                del database[date]
                self.logger.debug(f"Removed old data for {date}")
            
            if old_dates:
                self.logger.info(f"Cleaned up {len(old_dates)} old data entries")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
    
    def get_historical_prices(self, days: int = None) -> List[float]:
        """
        Get historical close prices for EMA initialization
        
        Args:
            days: Number of days to retrieve (defaults to all available)
            
        Returns:
            List of close prices in chronological order
        """
        try:
            database = self.load_database()
            
            if not database:
                self.logger.warning("No historical data available")
                return []
            
            # Get sorted dates
            sorted_dates = sorted(database.keys())
            
            if days:
                sorted_dates = sorted_dates[-days:]
            
            prices = []
            
            for date in sorted_dates:
                daily_candles = database[date]
                for candle in daily_candles:
                    prices.append(float(candle['close']))
            
            self.logger.info(f"Retrieved {len(prices)} historical prices from {len(sorted_dates)} days")
            return prices
            
        except Exception as e:
            self.logger.error(f"Error retrieving historical prices: {e}")
            return []
    
    def get_database_info(self) -> Dict:
        """
        Get information about the historical database
        
        Returns:
            Dictionary with database statistics
        """
        try:
            database = self.load_database()
            
            if not database:
                return {'status': 'empty', 'total_days': 0, 'total_candles': 0}
            
            total_candles = sum(len(daily_data) for daily_data in database.values())
            date_range = {
                'start': min(database.keys()),
                'end': max(database.keys())
            }
            
            # Load metadata if available
            metadata = {}
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r') as f:
                    metadata = json.load(f)
            
            return {
                'status': 'loaded',
                'total_days': len(database),
                'total_candles': total_candles,
                'date_range': date_range,
                'last_updated': metadata.get('last_updated'),
                'database_file': str(self.db_file),
                'size_mb': self.db_file.stat().st_size / (1024 * 1024) if self.db_file.exists() else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting database info: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def is_update_needed(self) -> bool:
        """
        Check if historical data update is needed
        
        Returns:
            True if update is needed
        """
        try:
            if not self.metadata_file.exists():
                return True
            
            with open(self.metadata_file, 'r') as f:
                metadata = json.load(f)
            
            last_updated = datetime.fromisoformat(metadata['last_updated'])
            now = datetime.now(self.timezone)
            
            # Update if last update was more than 1 day ago
            if (now - last_updated).days >= 1:
                return True
            
            # Update if we don't have today's data and market has opened
            today = now.date().strftime('%Y-%m-%d')
            database = self.load_database()
            
            if today not in database:
                # Check if market has been open for at least 30 minutes
                market_start_today = self.timezone.localize(
                    datetime.combine(now.date(), datetime.strptime(self.market_start, '%H:%M').time())
                )
                
                if now > market_start_today + timedelta(minutes=30):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking update need: {e}")
            return True
