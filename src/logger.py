#!/usr/bin/env python3
"""
Signal Logger Module
===================

This module handles CSV logging of EMA crossover signals with P&L tracking.
It creates separate CSV files for each timeframe and maintains running totals
of profits and losses for performance analysis.

Key Features:
- Separate CSV files per timeframe
- Comprehensive signal logging with OHLC data
- P&L tracking and cumulative calculations
- Thread-safe file operations
- Automatic file rotation and backup
- Performance statistics

CSV Columns:
- Datetime: Signal timestamp
- Action: BUY/SELL signal type
- Price: Signal price
- EMA Combo: EMA combination (e.g., "5/10")
- Entry/Exit: Position entry or exit
- P&L: Profit/Loss for the signal
- Short EMA: Short EMA value
- Long EMA: Long EMA value
- Candle Open/High/Low/Close: OHLC data
- Volume: Candle volume
- Cumulative P&L: Running total

Author: AI Assistant
Date: 2025
"""

import os
import csv
import logging
import threading
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path


class SignalLogger:
    """
    CSV Signal Logger with P&L Tracking

    Logs EMA crossover signals to separate CSV files per timeframe.
    Maintains running P&L calculations and provides performance statistics.
    """

    def __init__(self, data_directory: str, timeframes: List[str], initial_capital: float = 100000):
        """
        Initialize signal logger

        Args:
            data_directory: Directory to store CSV files
            timeframes: List of timeframes to create files for
            initial_capital: Initial capital for P&L calculations
        """
        self.data_directory = Path(data_directory)
        self.timeframes = timeframes
        self.initial_capital = initial_capital

        # Create data directory
        self.data_directory.mkdir(exist_ok=True)

        # CSV file handles and writers
        self.csv_files = {}
        self.csv_writers = {}

        # P&L tracking per timeframe
        self.cumulative_pnl = {tf: 0.0 for tf in timeframes}
        self.trade_count = {tf: 0 for tf in timeframes}

        # Thread lock for file operations
        self.lock = threading.Lock()

        # CSV headers
        self.csv_headers = [
            'Datetime',
            'Action',
            'Price',
            'EMA_Combo',
            'Entry_Exit',
            'PnL',
            'Short_EMA_Value',
            'Long_EMA_Value',
            'Candle_Open',
            'Candle_High',
            'Candle_Low',
            'Candle_Close',
            'Candle_Volume',
            'Cumulative_PnL'
        ]

        self.logger = logging.getLogger(__name__)

        # Initialize CSV files
        self._initialize_csv_files()

        self.logger.info(f"Signal Logger initialized for timeframes: {timeframes}")

    def _initialize_csv_files(self):
        """Initialize CSV files for each timeframe"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for timeframe in self.timeframes:
            try:
                # Create filename
                filename = f"nifty50_ema_signals_{timeframe}_{timestamp}.csv"
                filepath = self.data_directory / filename

                # Open file and create CSV writer
                file_handle = open(filepath, 'w', newline='', encoding='utf-8')
                csv_writer = csv.DictWriter(file_handle, fieldnames=self.csv_headers)

                # Write header
                csv_writer.writeheader()
                file_handle.flush()

                # Store references
                self.csv_files[timeframe] = file_handle
                self.csv_writers[timeframe] = csv_writer

                self.logger.info(f"Created CSV file for {timeframe}: {filepath}")

            except Exception as e:
                self.logger.error(f"Error creating CSV file for {timeframe}: {e}")

    def log_signal(self, signal_data: Dict):
        """
        Log a signal to the appropriate CSV file

        Args:
            signal_data: Dictionary containing signal information
        """
        try:
            timeframe = signal_data.get('timeframe')
            if not timeframe or timeframe not in self.csv_writers:
                self.logger.warning(f"Invalid timeframe for logging: {timeframe}")
                return

            with self.lock:
                # Update P&L tracking
                pnl = signal_data.get('pnl', 0)
                self.cumulative_pnl[timeframe] += pnl
                self.trade_count[timeframe] += 1

                # Prepare CSV row
                candle_data = signal_data.get('candle_data', {})

                csv_row = {
                    'Datetime': signal_data.get('datetime', datetime.now()).strftime('%Y-%m-%d %H:%M:%S'),
                    'Action': signal_data.get('action', ''),
                    'Price': f"{signal_data.get('price', 0):.2f}",
                    'EMA_Combo': signal_data.get('ema_combo', ''),
                    'Entry_Exit': signal_data.get('entry_exit', ''),
                    'PnL': f"{pnl:.2f}",
                    'Short_EMA_Value': f"{signal_data.get('short_ema_value', 0):.2f}",
                    'Long_EMA_Value': f"{signal_data.get('long_ema_value', 0):.2f}",
                    'Candle_Open': f"{candle_data.get('open', 0):.2f}" if candle_data.get('open') else '',
                    'Candle_High': f"{candle_data.get('high', 0):.2f}" if candle_data.get('high') else '',
                    'Candle_Low': f"{candle_data.get('low', 0):.2f}" if candle_data.get('low') else '',
                    'Candle_Close': f"{candle_data.get('close', 0):.2f}" if candle_data.get('close') else '',
                    'Candle_Volume': str(candle_data.get('volume', 0)),
                    'Cumulative_PnL': f"{self.cumulative_pnl[timeframe]:.2f}"
                }

                # Write to CSV
                self.csv_writers[timeframe].writerow(csv_row)
                self.csv_files[timeframe].flush()

                self.logger.debug(f"Logged signal for {timeframe}: {csv_row}")

        except Exception as e:
            self.logger.error(f"Error logging signal: {e}")

    def get_statistics(self) -> Dict:
        """
        Get logging statistics

        Returns:
            Dictionary with statistics for each timeframe
        """
        stats = {
            'total_signals': sum(self.trade_count.values()),
            'timeframes': {}
        }

        for timeframe in self.timeframes:
            stats['timeframes'][timeframe] = {
                'signal_count': self.trade_count[timeframe],
                'cumulative_pnl': self.cumulative_pnl[timeframe],
                'average_pnl': (self.cumulative_pnl[timeframe] / self.trade_count[timeframe]
                               if self.trade_count[timeframe] > 0 else 0),
                'csv_file': str(self.data_directory / f"nifty50_ema_signals_{timeframe}_*.csv")
            }

        return stats

    def close(self):
        """Close all CSV files"""
        with self.lock:
            for timeframe, file_handle in self.csv_files.items():
                try:
                    file_handle.close()
                    self.logger.info(f"Closed CSV file for {timeframe}")
                except Exception as e:
                    self.logger.error(f"Error closing CSV file for {timeframe}: {e}")

            self.csv_files.clear()
            self.csv_writers.clear()

    def create_summary_report(self) -> str:
        """
        Create a summary report of all signals

        Returns:
            String containing formatted summary report
        """
        report = []
        report.append("=" * 60)
        report.append("EMA CROSSOVER TRADING SUMMARY REPORT")
        report.append("=" * 60)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Initial Capital: ₹{self.initial_capital:,.2f}")
        report.append("")

        total_signals = sum(self.trade_count.values())
        total_pnl = sum(self.cumulative_pnl.values())

        report.append(f"Overall Performance:")
        report.append(f"  Total Signals: {total_signals}")
        report.append(f"  Total P&L: ₹{total_pnl:,.2f}")
        report.append(f"  Return %: {(total_pnl / self.initial_capital * 100):.2f}%")
        report.append("")

        report.append("Timeframe Breakdown:")
        report.append("-" * 40)

        for timeframe in self.timeframes:
            signals = self.trade_count[timeframe]
            pnl = self.cumulative_pnl[timeframe]
            avg_pnl = pnl / signals if signals > 0 else 0

            report.append(f"{timeframe:>8}: {signals:>3} signals | "
                         f"P&L: ₹{pnl:>8.2f} | Avg: ₹{avg_pnl:>6.2f}")

        report.append("")
        report.append("CSV Files Location:")
        report.append(f"  {self.data_directory}")
        report.append("=" * 60)

        return "\n".join(report)