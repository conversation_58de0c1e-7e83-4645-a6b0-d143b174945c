#!/usr/bin/env python3
"""
Simple EMA Test
===============

Test EMA calculation and crossover detection directly.
"""

import sys
import os
sys.path.append('src')

from ema import EMACalculator

def test_ema_crossover():
    """Test EMA crossover detection"""
    print("Testing EMA Crossover Detection...")
    
    # Create EMA calculator
    ema_combinations = [{"short_ema": 5, "long_ema": 10}]
    calculator = EMACalculator(ema_combinations)
    
    # Test prices that should create crossovers
    # Start high, go down (death cross), then go up (golden cross)
    prices = [
        # Initial high prices
        100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86,
        # Reversal - prices going up
        87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105
    ]
    
    print(f"Processing {len(prices)} prices...")
    
    signals_found = []
    
    for i, price in enumerate(prices):
        # Add price to calculator
        emas = calculator.add_price("1min", price)
        
        # Check for signals
        signals = calculator.get_crossover_signals("1min")
        
        if signals:
            signals_found.extend(signals)
            for signal in signals:
                print(f"🔔 SIGNAL {len(signals_found)}: {signal['signal']} at price {price}")
                print(f"   EMA{signal['short_ema']}: {signal['short_value']:.2f}")
                print(f"   EMA{signal['long_ema']}: {signal['long_value']:.2f}")
        
        # Show EMA values every 5 prices
        if i % 5 == 0 and emas:
            ema5 = emas.get(5, 0)
            ema10 = emas.get(10, 0)
            print(f"Price {price:3.0f}: EMA5={ema5:6.2f}, EMA10={ema10:6.2f}")
    
    print(f"\nTotal signals found: {len(signals_found)}")
    
    if len(signals_found) >= 2:
        print("✅ SUCCESS: Found both SELL and BUY signals!")
        return True
    else:
        print("❌ ISSUE: Expected at least 2 signals (SELL and BUY)")
        return False

def main():
    print("=" * 50)
    print("SIMPLE EMA CROSSOVER TEST")
    print("=" * 50)
    
    success = test_ema_crossover()
    
    if success:
        print("\n🎉 EMA crossover detection is working!")
        print("The issue might be in candle generation or data flow.")
    else:
        print("\n❌ EMA crossover detection has issues.")
    
    print("\nNext: Check the full system with proper candle generation.")

if __name__ == "__main__":
    main()
