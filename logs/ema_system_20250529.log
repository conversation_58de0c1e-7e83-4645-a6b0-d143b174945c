2025-05-29 11:10:55,555 - __main__ - INFO - Data directory: data
2025-05-29 11:10:55,555 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:10:55,555 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:10:55,556 - __main__ - WARNING - DhanHQ credentials not configured - will use Mock Market Feed for testing
2025-05-29 11:10:55,556 - __main__ - INFO - To use live data, update your credentials in config/config.json
2025-05-29 11:10:55,556 - __main__ - INFO - Get your credentials from: https://web.dhan.co -> My Profile -> Access DhanHQ APIs
2025-05-29 11:10:55,556 - ema - INFO - EMA Calculator initialized with periods: [5, 8, 10, 12, 21, 26]
2025-05-29 11:10:55,556 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_111055.csv
2025-05-29 11:10:55,556 - logger - INFO - Created CSV file for 5min: data/nifty50_ema_signals_5min_20250529_111055.csv
2025-05-29 11:10:55,556 - logger - INFO - Created CSV file for 10min: data/nifty50_ema_signals_10min_20250529_111055.csv
2025-05-29 11:10:55,556 - logger - INFO - Signal Logger initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:10:55,556 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:10:55,556 - __main__ - INFO - Using Mock Market Feed for testing
2025-05-29 11:10:55,556 - market_feed - INFO - Mock Market Feed initialized for testing
2025-05-29 11:10:55,556 - __main__ - INFO - All components initialized successfully
2025-05-29 11:10:55,556 - __main__ - INFO - System started successfully. Press Ctrl+C to stop.
2025-05-29 11:10:55,556 - market_feed - INFO - Starting mock market feed...
2025-05-29 11:11:10,125 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 11:11:10,125 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 11:11:10,125 - market_feed - INFO - Stopping mock market feed...
2025-05-29 11:11:10,125 - logger - INFO - Closed CSV file for 1min
2025-05-29 11:11:10,125 - logger - INFO - Closed CSV file for 5min
2025-05-29 11:11:10,126 - logger - INFO - Closed CSV file for 10min
2025-05-29 11:11:10,126 - __main__ - INFO - System stopped successfully
2025-05-29 11:11:10,246 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 11:19:22,882 - __main__ - INFO - Data directory: data
2025-05-29 11:19:22,882 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:19:22,882 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:19:22,882 - ema - INFO - EMA Calculator initialized with periods: [5, 8, 10, 12, 21, 26]
2025-05-29 11:19:22,882 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_111922.csv
2025-05-29 11:19:22,883 - logger - INFO - Created CSV file for 5min: data/nifty50_ema_signals_5min_20250529_111922.csv
2025-05-29 11:19:22,883 - logger - INFO - Created CSV file for 10min: data/nifty50_ema_signals_10min_20250529_111922.csv
2025-05-29 11:19:22,883 - logger - INFO - Signal Logger initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:19:22,883 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:19:22,883 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 11:19:22,883 - __main__ - ERROR - Error initializing components: DhanHQ and websocket libraries are required for live market feed. Install with: pip install dhanhq websocket-client
2025-05-29 11:19:48,211 - __main__ - INFO - Data directory: data
2025-05-29 11:19:48,211 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:19:48,211 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:19:48,211 - ema - INFO - EMA Calculator initialized with periods: [5, 8, 10, 12, 21, 26]
2025-05-29 11:19:48,212 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_111948.csv
2025-05-29 11:19:48,212 - logger - INFO - Created CSV file for 5min: data/nifty50_ema_signals_5min_20250529_111948.csv
2025-05-29 11:19:48,212 - logger - INFO - Created CSV file for 10min: data/nifty50_ema_signals_10min_20250529_111948.csv
2025-05-29 11:19:48,212 - logger - INFO - Signal Logger initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:19:48,212 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:19:48,212 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 11:19:48,212 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 11:19:48,212 - __main__ - INFO - All components initialized successfully
2025-05-29 11:19:48,212 - __main__ - INFO - System started successfully. Press Ctrl+C to stop.
2025-05-29 11:19:48,212 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 11:19:48,212 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:19:48,212 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:19:48,486 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:48 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:19:48,486 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:48 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:19:48,486 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:19:48,486 - market_feed - INFO - Scheduling reconnection attempt 1 in 5 seconds...
2025-05-29 11:19:53,487 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:19:53,665 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:53 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:19:53,665 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:53 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:19:53,666 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:19:53,666 - market_feed - INFO - Scheduling reconnection attempt 2 in 5 seconds...
2025-05-29 11:19:58,667 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:19:58,963 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:58 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:19:58,964 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:58 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:19:58,964 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:19:58,964 - market_feed - INFO - Scheduling reconnection attempt 3 in 5 seconds...
2025-05-29 11:20:03,965 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:04,137 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:04 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:04,137 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:04 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:04,137 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:04,138 - market_feed - INFO - Scheduling reconnection attempt 4 in 5 seconds...
2025-05-29 11:20:09,138 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:10,589 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:10 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:10,589 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:10 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:10,589 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:10,589 - market_feed - INFO - Scheduling reconnection attempt 5 in 5 seconds...
2025-05-29 11:20:15,591 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:15,928 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:15 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:15,929 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:15 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:15,929 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:15,929 - market_feed - INFO - Scheduling reconnection attempt 6 in 5 seconds...
2025-05-29 11:20:20,930 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:21,117 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:21 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:21,118 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:21 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:21,118 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:21,118 - market_feed - INFO - Scheduling reconnection attempt 7 in 5 seconds...
2025-05-29 11:20:26,119 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:26,310 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:26 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:26,310 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:26 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:26,310 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:26,311 - market_feed - INFO - Scheduling reconnection attempt 8 in 5 seconds...
2025-05-29 11:20:31,311 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:31,593 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:31 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:31,593 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:31 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:31,593 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:31,594 - market_feed - INFO - Scheduling reconnection attempt 9 in 5 seconds...
2025-05-29 11:20:36,594 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:36,759 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:36 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:36,759 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:36 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:36,759 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:36,759 - market_feed - INFO - Scheduling reconnection attempt 10 in 5 seconds...
2025-05-29 11:20:41,760 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:42,181 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:42 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:42,181 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:42 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:42,181 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:38:52,723 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 11:38:52,723 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 11:38:52,723 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 11:38:52,723 - market_feed - INFO - Market feed stopped
2025-05-29 11:38:52,723 - logger - INFO - Closed CSV file for 1min
2025-05-29 11:38:52,723 - logger - INFO - Closed CSV file for 5min
2025-05-29 11:38:52,724 - logger - INFO - Closed CSV file for 10min
2025-05-29 11:38:52,724 - __main__ - INFO - System stopped successfully
2025-05-29 11:46:16,974 - __main__ - INFO - Data directory: data
2025-05-29 11:46:16,974 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:46:16,975 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:46:16,975 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 11:46:16,975 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_114616.csv
2025-05-29 11:46:16,975 - logger - INFO - Signal Logger initialized for timeframes: ['1min']
2025-05-29 11:46:16,975 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 11:46:16,975 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 11:46:16,975 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 11:46:16,975 - __main__ - INFO - All components initialized successfully
2025-05-29 11:46:16,975 - __main__ - INFO - System started successfully. Press Ctrl+C to stop.
2025-05-29 11:46:16,975 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 11:46:16,976 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:46:16,976 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:46:17,306 - websocket - INFO - Websocket connected
2025-05-29 11:46:17,307 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 11:46:17,308 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:46:17,340 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 11:46:46,976 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:46:51,977 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:46:56,978 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:01,979 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:06,979 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:11,980 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:16,981 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:21,981 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:26,982 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:31,982 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:36,983 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:41,984 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:46,984 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:51,985 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:56,985 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:01,986 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:06,986 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:11,987 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:16,987 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:21,988 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:26,988 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:31,989 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:36,990 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:41,990 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:46,990 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:51,991 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:56,992 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:01,993 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:06,993 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:11,994 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:16,994 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:21,995 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:26,996 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:31,289 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 11:49:31,290 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 11:49:31,290 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 11:49:31,324 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:49:31,325 - market_feed - INFO - Market feed stopped
2025-05-29 11:49:31,325 - logger - INFO - Closed CSV file for 1min
2025-05-29 11:49:31,325 - __main__ - INFO - System stopped successfully
2025-05-29 11:54:11,557 - __main__ - INFO - Data directory: data
2025-05-29 11:54:11,558 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:54:11,558 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:54:11,558 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 11:54:11,558 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_115411.csv
2025-05-29 11:54:11,558 - logger - INFO - Signal Logger initialized for timeframes: ['1min']
2025-05-29 11:54:11,559 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 11:54:11,559 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 11:54:11,559 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 11:54:11,559 - __main__ - INFO - All components initialized successfully
2025-05-29 11:54:11,559 - __main__ - INFO - System started successfully. Press Ctrl+C to stop.
2025-05-29 11:54:11,559 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 11:54:11,559 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:54:11,559 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:54:12,013 - websocket - INFO - Websocket connected
2025-05-29 11:54:12,014 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 11:54:12,015 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:54:12,043 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0


2025-05-29 12:09:00,339 - strategy - INFO - 🔔 SELL Signal: 1min 5_10 @ 24737.70 (EMA5: 24739.01, EMA10: 24739.20) P&L: 0.00
2025-05-29 12:22:00,410 - strategy - INFO - 🔔 BUY Signal: 1min 5_10 @ 24737.70 (EMA5: 24734.43, EMA10: 24734.33) P&L: 0.00
2025-05-29 12:22:04,134 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 12:22:04,135 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 12:22:04,135 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 12:22:04,210 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 12:22:04,212 - market_feed - INFO - Market feed stopped
2025-05-29 12:22:04,212 - logger - INFO - Closed CSV file for 1min
2025-05-29 12:22:04,212 - __main__ - INFO - System stopped successfully
2025-05-29 12:27:55,994 - __main__ - INFO - Data directory: data
2025-05-29 12:27:55,995 - __main__ - INFO - EMA Trading System initialized
2025-05-29 12:27:55,995 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 12:27:56,005 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 12:27:56,005 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 12:27:56,005 - logger - INFO - Created new daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 12:27:56,005 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 12:27:56,005 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 12:27:56,006 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 12:27:56,006 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 12:27:56,006 - __main__ - INFO - All components initialized successfully
2025-05-29 12:27:56,006 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 47m
2025-05-29 12:27:56,006 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 12:27:56,006 - ema - INFO - Reset all EMA calculator data
2025-05-29 12:27:56,006 - logger - INFO - Daily state reset for new trading session
2025-05-29 12:27:56,006 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 12:27:56,006 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 12:27:56,006 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 12:27:56,006 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 12:27:56,006 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:27:56,006 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 12:27:56,310 - websocket - INFO - Websocket connected
2025-05-29 12:27:56,311 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 12:27:56,312 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:27:56,343 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 12:44:38,513 - __main__ - INFO - Data directory: data
2025-05-29 12:44:38,513 - __main__ - INFO - EMA Trading System initialized
2025-05-29 12:44:38,513 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 12:44:38,523 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 12:44:38,523 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 12:44:38,524 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 12:44:38,524 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 12:44:38,524 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 12:44:38,524 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 12:44:38,524 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 12:44:38,524 - __main__ - INFO - All components initialized successfully
2025-05-29 12:44:38,525 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 30m
2025-05-29 12:44:38,525 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 12:44:38,525 - ema - INFO - Reset all EMA calculator data
2025-05-29 12:44:38,525 - logger - INFO - Daily state reset for new trading session
2025-05-29 12:44:38,525 - state_manager - INFO - Daily state reset
2025-05-29 12:44:38,525 - __main__ - INFO - 🚀 System started in FOREGROUND mode. Press Ctrl+C to stop.
2025-05-29 12:44:38,525 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 12:44:38,525 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:44:38,525 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 12:44:38,847 - websocket - INFO - Websocket connected
2025-05-29 12:44:38,848 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 12:44:38,849 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:44:38,877 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 12:45:08,088 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 12:45:08,089 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 12:45:08,089 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 12:45:08,117 - market_feed - INFO - Market feed stopped
2025-05-29 12:45:08,118 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 12:45:08,122 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 12:45:08,123 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 12:45:08,123 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 12:45:08,124 - __main__ - INFO - System stopped successfully
2025-05-29 12:46:01,533 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 12:46:01,533 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 12:46:01,533 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 12:46:01,562 - market_feed - INFO - Market feed stopped
2025-05-29 12:46:01,562 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 12:46:01,563 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 12:46:01,563 - __main__ - INFO - System stopped successfully
2025-05-29 12:47:27,107 - __main__ - INFO - Data directory: data
2025-05-29 12:47:27,108 - __main__ - INFO - EMA Trading System initialized
2025-05-29 12:47:27,108 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 12:47:27,119 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 12:47:27,119 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 12:47:27,120 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 12:47:27,120 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 12:47:27,120 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 12:47:27,120 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 12:47:27,120 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 12:47:27,120 - __main__ - INFO - All components initialized successfully
2025-05-29 12:47:27,121 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 27m
2025-05-29 12:47:27,121 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 12:47:27,121 - ema - INFO - Reset all EMA calculator data
2025-05-29 12:47:27,121 - logger - INFO - Daily state reset for new trading session
2025-05-29 12:47:27,121 - state_manager - INFO - Daily state reset
2025-05-29 12:47:27,121 - __main__ - INFO - 🚀 System started in FOREGROUND mode. Press Ctrl+C to stop.
2025-05-29 12:47:27,121 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 12:47:27,121 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:47:27,121 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 12:47:27,407 - websocket - INFO - Websocket connected
2025-05-29 12:47:27,408 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 12:47:27,409 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:47:27,439 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 12:47:46,707 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 12:47:46,707 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 12:47:46,708 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 12:47:46,741 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 12:47:46,744 - market_feed - INFO - Market feed stopped
2025-05-29 12:47:46,745 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 12:47:46,746 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 12:47:46,746 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 12:47:46,746 - __main__ - INFO - System stopped successfully
2025-05-29 12:48:44,441 - __main__ - INFO - Data directory: data
2025-05-29 12:48:44,441 - __main__ - INFO - EMA Trading System initialized
2025-05-29 12:48:44,441 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 12:48:44,452 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 12:48:44,452 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 12:48:44,452 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 12:48:44,452 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 12:48:44,452 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 12:48:44,452 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 12:48:44,452 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 12:48:44,452 - __main__ - INFO - All components initialized successfully
2025-05-29 12:48:44,453 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 26m
2025-05-29 12:48:44,453 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 12:48:44,453 - ema - INFO - Reset all EMA calculator data
2025-05-29 12:48:44,453 - logger - INFO - Daily state reset for new trading session
2025-05-29 12:48:44,453 - state_manager - INFO - Daily state reset
2025-05-29 12:48:44,453 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 12:48:44,453 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 12:48:44,453 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 12:48:44,453 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 12:48:44,453 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:48:44,453 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 12:48:44,708 - websocket - INFO - Websocket connected
2025-05-29 12:48:44,709 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 12:48:44,709 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:48:44,738 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 13:00:59,878 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 13:00:59,878 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 13:00:59,879 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 13:00:59,906 - market_feed - INFO - Market feed stopped
2025-05-29 13:00:59,906 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:00:59,906 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 13:00:59,906 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 13:00:59,906 - __main__ - INFO - System stopped successfully
2025-05-29 13:01:09,857 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 13:03:15,568 - __main__ - INFO - Data directory: data
2025-05-29 13:03:15,568 - __main__ - INFO - EMA Trading System initialized
2025-05-29 13:03:15,568 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 13:03:15,579 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 13:03:15,579 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 13:03:15,580 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 13:03:15,580 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 13:03:15,580 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 13:03:15,580 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 13:03:15,580 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 13:03:15,580 - __main__ - INFO - All components initialized successfully
2025-05-29 13:03:15,580 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 11m
2025-05-29 13:03:15,580 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 13:03:15,580 - ema - INFO - Reset all EMA calculator data
2025-05-29 13:03:15,580 - logger - INFO - Daily state reset for new trading session
2025-05-29 13:03:15,580 - state_manager - INFO - Daily state reset
2025-05-29 13:03:15,581 - historical_data - INFO - Market open for 3.8 hours with no signals - recovery needed
2025-05-29 13:03:15,581 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 13:03:15,581 - historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,581 - historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,896 - historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:03:15,897 - historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 13:03:15,897 - historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,898 - historical_data - INFO - Generated 229 mock historical candles
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #1 logged: SELL @ 24721.87
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 09:25 @ 24721.87
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #2 logged: BUY @ 24707.06
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical BUY signal at 09:38 @ 24707.06
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #3 logged: SELL @ 24787.47
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 10:04 @ 24787.47
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #4 logged: BUY @ 24833.09
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical BUY signal at 10:08 @ 24833.09
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #5 logged: SELL @ 24790.24
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 10:17 @ 24790.24
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #6 logged: BUY @ 24841.81
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 10:24 @ 24841.81
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #7 logged: SELL @ 25064.92
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 10:58 @ 25064.92
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #8 logged: BUY @ 25119.66
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:09 @ 25119.66
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #9 logged: SELL @ 25027.72
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 11:16 @ 25027.72
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #10 logged: BUY @ 25113.26
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:24 @ 25113.26
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #11 logged: SELL @ 25030.76
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 11:26 @ 25030.76
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #12 logged: BUY @ 25046.41
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:35 @ 25046.41
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #13 logged: SELL @ 25258.03
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:01 @ 25258.03
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #14 logged: BUY @ 25301.63
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:02 @ 25301.63
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #15 logged: SELL @ 25269.30
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:10 @ 25269.30
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #16 logged: BUY @ 25310.95
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:15 @ 25310.95
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #17 logged: SELL @ 25335.76
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:27 @ 25335.76
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #18 logged: BUY @ 25343.37
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:37 @ 25343.37
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #19 logged: SELL @ 25370.05
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:50 @ 25370.05
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #20 logged: BUY @ 25408.15
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:51 @ 25408.15
2025-05-29 13:03:15,901 - logger - INFO - 📝 Signal #21 logged: SELL @ 25395.60
2025-05-29 13:03:15,901 - historical_data - INFO - 📈 Historical SELL signal at 12:59 @ 25395.60
2025-05-29 13:03:15,901 - historical_data - INFO - Recovery complete: Found 21 historical crossovers
2025-05-29 13:03:15,901 - __main__ - INFO - ✅ Recovered 21 historical crossovers
2025-05-29 13:03:15,901 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:03:15,901 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 13:03:15,901 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 13:03:15,901 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 13:03:15,901 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 13:03:15,901 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:03:15,901 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 13:03:16,512 - websocket - INFO - Websocket connected
2025-05-29 13:03:16,513 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 13:03:16,514 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:03:16,545 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 13:21:13,459 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 13:21:13,459 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 13:21:13,459 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 13:21:13,488 - market_feed - INFO - Market feed stopped
2025-05-29 13:21:13,488 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 13:21:13,490 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:21:13,490 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 13:21:13,490 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 13:21:13,490 - __main__ - INFO - System stopped successfully
2025-05-29 13:22:11,960 - __main__ - INFO - Data directory: data
2025-05-29 13:22:11,960 - __main__ - INFO - EMA Trading System initialized
2025-05-29 13:22:11,961 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 13:22:11,971 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 13:22:11,972 - historical_database - INFO - Historical Database initialized: data/historical
2025-05-29 13:22:11,972 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 13:22:11,972 - logger - INFO - Loaded daily state: 21 signals, P&L: 0.00
2025-05-29 13:22:11,972 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 13:22:11,972 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 13:22:11,972 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 13:22:11,972 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 13:22:11,972 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 13:22:11,972 - __main__ - INFO - All components initialized successfully
2025-05-29 13:22:11,973 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 1h 52m
2025-05-29 13:22:11,973 - __main__ - INFO - 📊 Checking historical database...
2025-05-29 13:22:11,973 - __main__ - INFO - 🔄 Updating 2-week historical database...
2025-05-29 13:22:11,973 - historical_database - INFO - Starting historical data update...
2025-05-29 13:22:11,973 - historical_database - INFO - No existing database found, starting fresh
2025-05-29 13:22:11,973 - historical_database - INFO - Fetching data for 2025-05-12
2025-05-29 13:22:12,682 - historical_database - ERROR - HTTP error 500 for 2025-05-12: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:12,682 - historical_database - WARNING - API failed for 2025-05-12, generating mock data
2025-05-29 13:22:12,683 - historical_database - INFO - Generating mock data for 2025-05-12
2025-05-29 13:22:12,698 - historical_database - INFO - Generated 360 mock candles for 2025-05-12
2025-05-29 13:22:12,698 - historical_database - INFO - Updated data for 2025-05-12: 360 candles
2025-05-29 13:22:13,199 - historical_database - INFO - Fetching data for 2025-05-13
2025-05-29 13:22:13,701 - historical_database - ERROR - HTTP error 500 for 2025-05-13: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:13,702 - historical_database - WARNING - API failed for 2025-05-13, generating mock data
2025-05-29 13:22:13,703 - historical_database - INFO - Generating mock data for 2025-05-13
2025-05-29 13:22:13,736 - historical_database - INFO - Generated 360 mock candles for 2025-05-13
2025-05-29 13:22:13,737 - historical_database - INFO - Updated data for 2025-05-13: 360 candles
2025-05-29 13:22:14,237 - historical_database - INFO - Fetching data for 2025-05-14
2025-05-29 13:22:14,398 - historical_database - ERROR - HTTP error 500 for 2025-05-14: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:14,398 - historical_database - WARNING - API failed for 2025-05-14, generating mock data
2025-05-29 13:22:14,398 - historical_database - INFO - Generating mock data for 2025-05-14
2025-05-29 13:22:14,416 - historical_database - INFO - Generated 360 mock candles for 2025-05-14
2025-05-29 13:22:14,416 - historical_database - INFO - Updated data for 2025-05-14: 360 candles
2025-05-29 13:22:14,917 - historical_database - INFO - Fetching data for 2025-05-15
2025-05-29 13:22:15,192 - historical_database - ERROR - HTTP error 500 for 2025-05-15: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:15,193 - historical_database - WARNING - API failed for 2025-05-15, generating mock data
2025-05-29 13:22:15,194 - historical_database - INFO - Generating mock data for 2025-05-15
2025-05-29 13:22:15,227 - historical_database - INFO - Generated 360 mock candles for 2025-05-15
2025-05-29 13:22:15,227 - historical_database - INFO - Updated data for 2025-05-15: 360 candles
2025-05-29 13:22:15,728 - historical_database - INFO - Fetching data for 2025-05-16
2025-05-29 13:22:16,697 - historical_database - ERROR - HTTP error 500 for 2025-05-16: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:16,697 - historical_database - WARNING - API failed for 2025-05-16, generating mock data
2025-05-29 13:22:16,698 - historical_database - INFO - Generating mock data for 2025-05-16
2025-05-29 13:22:16,731 - historical_database - INFO - Generated 360 mock candles for 2025-05-16
2025-05-29 13:22:16,731 - historical_database - INFO - Updated data for 2025-05-16: 360 candles
2025-05-29 13:22:17,232 - historical_database - INFO - Fetching data for 2025-05-19
2025-05-29 13:22:19,850 - historical_database - ERROR - HTTP error 500 for 2025-05-19: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:19,850 - historical_database - WARNING - API failed for 2025-05-19, generating mock data
2025-05-29 13:22:19,851 - historical_database - INFO - Generating mock data for 2025-05-19
2025-05-29 13:22:19,884 - historical_database - INFO - Generated 360 mock candles for 2025-05-19
2025-05-29 13:22:19,884 - historical_database - INFO - Updated data for 2025-05-19: 360 candles
2025-05-29 13:22:20,385 - historical_database - INFO - Fetching data for 2025-05-20
2025-05-29 13:22:21,268 - historical_database - ERROR - HTTP error 500 for 2025-05-20: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:21,269 - historical_database - WARNING - API failed for 2025-05-20, generating mock data
2025-05-29 13:22:21,269 - historical_database - INFO - Generating mock data for 2025-05-20
2025-05-29 13:22:21,277 - historical_database - INFO - Generated 360 mock candles for 2025-05-20
2025-05-29 13:22:21,277 - historical_database - INFO - Updated data for 2025-05-20: 360 candles
2025-05-29 13:22:21,777 - historical_database - INFO - Fetching data for 2025-05-21
2025-05-29 13:22:22,043 - historical_database - ERROR - HTTP error 500 for 2025-05-21: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:22,043 - historical_database - WARNING - API failed for 2025-05-21, generating mock data
2025-05-29 13:22:22,044 - historical_database - INFO - Generating mock data for 2025-05-21
2025-05-29 13:22:22,057 - historical_database - INFO - Generated 360 mock candles for 2025-05-21
2025-05-29 13:22:22,057 - historical_database - INFO - Updated data for 2025-05-21: 360 candles
2025-05-29 13:22:22,557 - historical_database - INFO - Fetching data for 2025-05-22
2025-05-29 13:22:23,307 - historical_database - ERROR - HTTP error 500 for 2025-05-22: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:23,308 - historical_database - WARNING - API failed for 2025-05-22, generating mock data
2025-05-29 13:22:23,308 - historical_database - INFO - Generating mock data for 2025-05-22
2025-05-29 13:22:23,337 - historical_database - INFO - Generated 360 mock candles for 2025-05-22
2025-05-29 13:22:23,337 - historical_database - INFO - Updated data for 2025-05-22: 360 candles
2025-05-29 13:22:23,838 - historical_database - INFO - Fetching data for 2025-05-23
2025-05-29 13:22:24,357 - historical_database - ERROR - HTTP error 500 for 2025-05-23: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:24,358 - historical_database - WARNING - API failed for 2025-05-23, generating mock data
2025-05-29 13:22:24,358 - historical_database - INFO - Generating mock data for 2025-05-23
2025-05-29 13:22:24,390 - historical_database - INFO - Generated 360 mock candles for 2025-05-23
2025-05-29 13:22:24,390 - historical_database - INFO - Updated data for 2025-05-23: 360 candles
2025-05-29 13:22:24,891 - historical_database - INFO - Fetching data for 2025-05-26
2025-05-29 13:22:25,069 - historical_database - ERROR - HTTP error 500 for 2025-05-26: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:25,071 - historical_database - WARNING - API failed for 2025-05-26, generating mock data
2025-05-29 13:22:25,071 - historical_database - INFO - Generating mock data for 2025-05-26
2025-05-29 13:22:25,101 - historical_database - INFO - Generated 360 mock candles for 2025-05-26
2025-05-29 13:22:25,101 - historical_database - INFO - Updated data for 2025-05-26: 360 candles
2025-05-29 13:22:25,602 - historical_database - INFO - Fetching data for 2025-05-27
2025-05-29 13:22:25,934 - historical_database - ERROR - HTTP error 500 for 2025-05-27: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:25,935 - historical_database - WARNING - API failed for 2025-05-27, generating mock data
2025-05-29 13:22:25,935 - historical_database - INFO - Generating mock data for 2025-05-27
2025-05-29 13:22:25,968 - historical_database - INFO - Generated 360 mock candles for 2025-05-27
2025-05-29 13:22:25,968 - historical_database - INFO - Updated data for 2025-05-27: 360 candles
2025-05-29 13:22:26,469 - historical_database - INFO - Fetching data for 2025-05-28
2025-05-29 13:22:26,647 - historical_database - ERROR - HTTP error 500 for 2025-05-28: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:26,648 - historical_database - WARNING - API failed for 2025-05-28, generating mock data
2025-05-29 13:22:26,649 - historical_database - INFO - Generating mock data for 2025-05-28
2025-05-29 13:22:26,683 - historical_database - INFO - Generated 360 mock candles for 2025-05-28
2025-05-29 13:22:26,683 - historical_database - INFO - Updated data for 2025-05-28: 360 candles
2025-05-29 13:22:27,184 - historical_database - INFO - Fetching data for 2025-05-29
2025-05-29 13:22:27,753 - historical_database - ERROR - HTTP error 500 for 2025-05-29: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:27,754 - historical_database - WARNING - API failed for 2025-05-29, generating mock data
2025-05-29 13:22:27,755 - historical_database - INFO - Generating mock data for 2025-05-29
2025-05-29 13:22:27,785 - historical_database - INFO - Generated 360 mock candles for 2025-05-29
2025-05-29 13:22:27,785 - historical_database - INFO - Updated data for 2025-05-29: 360 candles
2025-05-29 13:22:28,321 - historical_database - INFO - Saved database with 14 days
2025-05-29 13:22:28,322 - historical_database - INFO - Historical data update complete: 14 days updated
2025-05-29 13:22:28,329 - historical_database - INFO - Loaded historical database with 14 days
2025-05-29 13:22:28,330 - __main__ - INFO - ✅ Historical database updated: 14 days, 5040 candles
2025-05-29 13:22:28,330 - __main__ - INFO - 🧮 Initializing EMAs with historical data...
2025-05-29 13:22:28,333 - historical_database - INFO - Loaded historical database with 14 days
2025-05-29 13:22:28,333 - historical_database - INFO - Retrieved 3600 historical prices from 10 days
2025-05-29 13:22:28,334 - ema - INFO - Loading EMA state from 3600 historical prices for 1min
2025-05-29 13:22:28,334 - ema - INFO - Reset data for timeframe: 1min
2025-05-29 13:22:28,338 - ema - INFO - EMA state loaded for 1min: EMA10=23961.84, EMA5=23945.28
2025-05-29 13:22:28,338 - __main__ - INFO - ✅ EMAs initialized with 3600 historical prices
2025-05-29 13:22:28,338 - __main__ - INFO - 📈 Current EMAs: EMA10=23961.84, EMA5=23945.28
2025-05-29 13:22:28,339 - __main__ - INFO - 🔄 New trading day detected - but EMAs already initialized with historical data
2025-05-29 13:22:28,339 - logger - INFO - Daily state reset for new trading session
2025-05-29 13:22:28,339 - state_manager - INFO - Daily state reset
2025-05-29 13:22:28,339 - historical_data - INFO - Market open for 4.1 hours with no signals - recovery needed
2025-05-29 13:22:28,339 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 13:22:28,339 - historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:22:28.339386+05:30
2025-05-29 13:22:28,339 - historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:22:28.339386+05:30
2025-05-29 13:22:28,635 - historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:28,636 - historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 13:22:28,636 - historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:22:28.339386+05:30
2025-05-29 13:22:28,640 - historical_data - INFO - Generated 248 mock historical candles
2025-05-29 13:22:28,641 - logger - INFO - 📝 Signal #1 logged: BUY @ 24761.35
2025-05-29 13:22:28,641 - historical_data - INFO - 📈 Historical BUY signal at 09:15 @ 24761.35
2025-05-29 13:22:28,642 - logger - INFO - 📝 Signal #2 logged: SELL @ 24843.32
2025-05-29 13:22:28,642 - historical_data - INFO - 📈 Historical SELL signal at 09:43 @ 24843.32
2025-05-29 13:22:28,642 - logger - INFO - 📝 Signal #3 logged: BUY @ 24877.44
2025-05-29 13:22:28,643 - historical_data - INFO - 📈 Historical BUY signal at 09:52 @ 24877.44
2025-05-29 13:22:28,643 - logger - INFO - 📝 Signal #4 logged: SELL @ 24888.64
2025-05-29 13:22:28,643 - historical_data - INFO - 📈 Historical SELL signal at 10:13 @ 24888.64
2025-05-29 13:22:28,644 - logger - INFO - 📝 Signal #5 logged: BUY @ 24945.07
2025-05-29 13:22:28,644 - historical_data - INFO - 📈 Historical BUY signal at 10:15 @ 24945.07
2025-05-29 13:22:28,644 - logger - INFO - 📝 Signal #6 logged: SELL @ 24926.35
2025-05-29 13:22:28,644 - historical_data - INFO - 📈 Historical SELL signal at 10:23 @ 24926.35
2025-05-29 13:22:28,645 - logger - INFO - 📝 Signal #7 logged: BUY @ 24686.89
2025-05-29 13:22:28,645 - historical_data - INFO - 📈 Historical BUY signal at 11:02 @ 24686.89
2025-05-29 13:22:28,646 - logger - INFO - 📝 Signal #8 logged: SELL @ 24746.32
2025-05-29 13:22:28,646 - historical_data - INFO - 📈 Historical SELL signal at 11:24 @ 24746.32
2025-05-29 13:22:28,646 - logger - INFO - 📝 Signal #9 logged: BUY @ 24785.50
2025-05-29 13:22:28,646 - historical_data - INFO - 📈 Historical BUY signal at 11:25 @ 24785.50
2025-05-29 13:22:28,647 - logger - INFO - 📝 Signal #10 logged: SELL @ 24773.70
2025-05-29 13:22:28,647 - historical_data - INFO - 📈 Historical SELL signal at 11:33 @ 24773.70
2025-05-29 13:22:28,647 - logger - INFO - 📝 Signal #11 logged: BUY @ 24791.74
2025-05-29 13:22:28,648 - historical_data - INFO - 📈 Historical BUY signal at 11:34 @ 24791.74
2025-05-29 13:22:28,648 - logger - INFO - 📝 Signal #12 logged: SELL @ 24779.43
2025-05-29 13:22:28,648 - historical_data - INFO - 📈 Historical SELL signal at 11:35 @ 24779.43
2025-05-29 13:22:28,648 - logger - INFO - 📝 Signal #13 logged: BUY @ 24792.85
2025-05-29 13:22:28,649 - historical_data - INFO - 📈 Historical BUY signal at 11:36 @ 24792.85
2025-05-29 13:22:28,649 - logger - INFO - 📝 Signal #14 logged: SELL @ 24932.40
2025-05-29 13:22:28,649 - historical_data - INFO - 📈 Historical SELL signal at 11:57 @ 24932.40
2025-05-29 13:22:28,650 - logger - INFO - 📝 Signal #15 logged: BUY @ 24905.23
2025-05-29 13:22:28,650 - historical_data - INFO - 📈 Historical BUY signal at 12:11 @ 24905.23
2025-05-29 13:22:28,650 - logger - INFO - 📝 Signal #16 logged: SELL @ 25006.33
2025-05-29 13:22:28,651 - historical_data - INFO - 📈 Historical SELL signal at 12:29 @ 25006.33
2025-05-29 13:22:28,651 - logger - INFO - 📝 Signal #17 logged: BUY @ 25075.28
2025-05-29 13:22:28,651 - historical_data - INFO - 📈 Historical BUY signal at 12:31 @ 25075.28
2025-05-29 13:22:28,652 - logger - INFO - 📝 Signal #18 logged: SELL @ 25138.94
2025-05-29 13:22:28,652 - historical_data - INFO - 📈 Historical SELL signal at 12:57 @ 25138.94
2025-05-29 13:22:28,652 - logger - INFO - 📝 Signal #19 logged: BUY @ 25209.61
2025-05-29 13:22:28,652 - historical_data - INFO - 📈 Historical BUY signal at 13:00 @ 25209.61
2025-05-29 13:22:28,653 - historical_data - INFO - Recovery complete: Found 19 historical crossovers
2025-05-29 13:22:28,653 - __main__ - INFO - ✅ Recovered 19 historical crossovers
2025-05-29 13:22:28,654 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:22:28,654 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 13:22:28,654 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 13:22:28,655 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 13:22:28,655 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 13:22:28,655 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:22:28,655 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 13:22:29,030 - websocket - INFO - Websocket connected
2025-05-29 13:22:29,031 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 13:22:29,032 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:22:29,062 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 13:23:00,356 - logger - INFO - 📝 Signal #20 logged: SELL @ 24755.10
2025-05-29 13:23:00,357 - strategy - INFO - 🔔 SELL Signal: 1min 5_10 @ 24755.10 (EMA5: 25260.63, EMA10: 25351.89) P&L: 0.00
2025-05-29 14:00:44,834 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 14:00:44,834 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 14:00:44,834 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 14:00:45,090 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 14:00:45,122 - market_feed - INFO - Market feed stopped
2025-05-29 14:00:45,123 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 14:00:45,123 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 14:00:45,124 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 14:00:45,124 - __main__ - INFO - System stopped successfully
2025-05-29 14:01:52,650 - __main__ - INFO - Data directory: data
2025-05-29 14:01:52,651 - __main__ - INFO - EMA Trading System initialized
2025-05-29 14:01:52,651 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 14:01:52,665 - utils.market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 14:01:52,666 - data.historical_database - INFO - Historical Database initialized: data/historical
2025-05-29 14:01:52,666 - core.ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 14:01:52,666 - data.logger - INFO - Loaded daily state: 41 signals, P&L: 0.00
2025-05-29 14:01:52,666 - data.logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 14:01:52,666 - data.logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 14:01:52,666 - core.strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 14:01:52,666 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 14:01:52,667 - core.market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 14:01:52,667 - __main__ - INFO - All components initialized successfully
2025-05-29 14:01:52,667 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 1h 13m
2025-05-29 14:01:52,667 - __main__ - INFO - 📊 Checking historical database...
2025-05-29 14:01:52,673 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:01:52,679 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:01:52,680 - __main__ - INFO - 📊 Historical database current: 14 days, 5040 candles
2025-05-29 14:01:52,680 - __main__ - INFO - 🧮 Initializing EMAs with historical data...
2025-05-29 14:01:52,685 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:01:52,686 - data.historical_database - INFO - Retrieved 3600 historical prices from 10 days
2025-05-29 14:01:52,686 - core.ema - INFO - Loading EMA state from 3600 historical prices for 1min
2025-05-29 14:01:52,687 - core.ema - INFO - Reset data for timeframe: 1min
2025-05-29 14:01:52,690 - core.ema - INFO - EMA state loaded for 1min: EMA10=23961.84, EMA5=23945.28
2025-05-29 14:01:52,690 - __main__ - INFO - ✅ EMAs initialized with 3600 historical prices
2025-05-29 14:01:52,691 - __main__ - INFO - 📈 Current EMAs: EMA10=23961.84, EMA5=23945.28
2025-05-29 14:01:52,691 - __main__ - INFO - 🔄 New trading day detected - but EMAs already initialized with historical data
2025-05-29 14:01:52,691 - data.logger - INFO - Daily state reset for new trading session
2025-05-29 14:01:52,691 - utils.state_manager - INFO - Daily state reset
2025-05-29 14:01:52,691 - data.historical_data - INFO - Market open for 4.8 hours with no signals - recovery needed
2025-05-29 14:01:52,691 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 14:01:52,691 - data.historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:01:52.691546+05:30
2025-05-29 14:01:52,691 - data.historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:01:52.691546+05:30
2025-05-29 14:01:56,269 - data.historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 14:01:56,269 - data.historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 14:01:56,270 - data.historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:01:52.691546+05:30
2025-05-29 14:01:56,275 - data.historical_data - INFO - Generated 287 mock historical candles
2025-05-29 14:01:56,276 - data.logger - INFO - 📝 Signal #1 logged: BUY @ 24757.55
2025-05-29 14:01:56,276 - data.historical_data - INFO - 📈 Historical BUY signal at 09:15 @ 24757.55
2025-05-29 14:01:56,276 - data.logger - INFO - 📝 Signal #2 logged: SELL @ 24782.00
2025-05-29 14:01:56,277 - data.historical_data - INFO - 📈 Historical SELL signal at 09:40 @ 24782.00
2025-05-29 14:01:56,277 - data.logger - INFO - 📝 Signal #3 logged: BUY @ 24759.64
2025-05-29 14:01:56,277 - data.historical_data - INFO - 📈 Historical BUY signal at 10:01 @ 24759.64
2025-05-29 14:01:56,278 - data.logger - INFO - 📝 Signal #4 logged: SELL @ 24825.75
2025-05-29 14:01:56,278 - data.historical_data - INFO - 📈 Historical SELL signal at 10:16 @ 24825.75
2025-05-29 14:01:56,278 - data.logger - INFO - 📝 Signal #5 logged: BUY @ 24767.65
2025-05-29 14:01:56,279 - data.historical_data - INFO - 📈 Historical BUY signal at 10:32 @ 24767.65
2025-05-29 14:01:56,279 - data.logger - INFO - 📝 Signal #6 logged: SELL @ 24722.62
2025-05-29 14:01:56,279 - data.historical_data - INFO - 📈 Historical SELL signal at 10:33 @ 24722.62
2025-05-29 14:01:56,279 - data.logger - INFO - 📝 Signal #7 logged: BUY @ 24706.36
2025-05-29 14:01:56,280 - data.historical_data - INFO - 📈 Historical BUY signal at 10:43 @ 24706.36
2025-05-29 14:01:56,280 - data.logger - INFO - 📝 Signal #8 logged: SELL @ 24784.04
2025-05-29 14:01:56,280 - data.historical_data - INFO - 📈 Historical SELL signal at 11:00 @ 24784.04
2025-05-29 14:01:56,281 - data.logger - INFO - 📝 Signal #9 logged: BUY @ 24839.77
2025-05-29 14:01:56,281 - data.historical_data - INFO - 📈 Historical BUY signal at 11:03 @ 24839.77
2025-05-29 14:01:56,281 - data.logger - INFO - 📝 Signal #10 logged: SELL @ 24765.60
2025-05-29 14:01:56,281 - data.historical_data - INFO - 📈 Historical SELL signal at 11:05 @ 24765.60
2025-05-29 14:01:56,282 - data.logger - INFO - 📝 Signal #11 logged: BUY @ 24815.95
2025-05-29 14:01:56,282 - data.historical_data - INFO - 📈 Historical BUY signal at 11:11 @ 24815.95
2025-05-29 14:01:56,282 - data.logger - INFO - 📝 Signal #12 logged: SELL @ 24887.82
2025-05-29 14:01:56,283 - data.historical_data - INFO - 📈 Historical SELL signal at 11:31 @ 24887.82
2025-05-29 14:01:56,283 - data.logger - INFO - 📝 Signal #13 logged: BUY @ 24455.23
2025-05-29 14:01:56,283 - data.historical_data - INFO - 📈 Historical BUY signal at 12:22 @ 24455.23
2025-05-29 14:01:56,284 - data.logger - INFO - 📝 Signal #14 logged: SELL @ 24436.21
2025-05-29 14:01:56,284 - data.historical_data - INFO - 📈 Historical SELL signal at 12:28 @ 24436.21
2025-05-29 14:01:56,284 - data.logger - INFO - 📝 Signal #15 logged: BUY @ 24473.14
2025-05-29 14:01:56,284 - data.historical_data - INFO - 📈 Historical BUY signal at 12:29 @ 24473.14
2025-05-29 14:01:56,285 - data.logger - INFO - 📝 Signal #16 logged: SELL @ 24441.64
2025-05-29 14:01:56,285 - data.historical_data - INFO - 📈 Historical SELL signal at 12:32 @ 24441.64
2025-05-29 14:01:56,285 - data.logger - INFO - 📝 Signal #17 logged: BUY @ 24406.40
2025-05-29 14:01:56,286 - data.historical_data - INFO - 📈 Historical BUY signal at 12:47 @ 24406.40
2025-05-29 14:01:56,286 - data.logger - INFO - 📝 Signal #18 logged: SELL @ 24365.79
2025-05-29 14:01:56,286 - data.historical_data - INFO - 📈 Historical SELL signal at 12:53 @ 24365.79
2025-05-29 14:01:56,286 - data.logger - INFO - 📝 Signal #19 logged: BUY @ 24401.39
2025-05-29 14:01:56,287 - data.historical_data - INFO - 📈 Historical BUY signal at 12:54 @ 24401.39
2025-05-29 14:01:56,287 - data.logger - INFO - 📝 Signal #20 logged: SELL @ 24369.82
2025-05-29 14:01:56,287 - data.historical_data - INFO - 📈 Historical SELL signal at 12:55 @ 24369.82
2025-05-29 14:01:56,287 - data.logger - INFO - 📝 Signal #21 logged: BUY @ 24364.54
2025-05-29 14:01:56,288 - data.historical_data - INFO - 📈 Historical BUY signal at 13:08 @ 24364.54
2025-05-29 14:01:56,288 - data.logger - INFO - 📝 Signal #22 logged: SELL @ 24494.88
2025-05-29 14:01:56,288 - data.historical_data - INFO - 📈 Historical SELL signal at 13:32 @ 24494.88
2025-05-29 14:01:56,289 - data.logger - INFO - 📝 Signal #23 logged: BUY @ 24526.37
2025-05-29 14:01:56,289 - data.historical_data - INFO - 📈 Historical BUY signal at 13:36 @ 24526.37
2025-05-29 14:01:56,289 - data.logger - INFO - 📝 Signal #24 logged: SELL @ 24498.12
2025-05-29 14:01:56,289 - data.historical_data - INFO - 📈 Historical SELL signal at 13:37 @ 24498.12
2025-05-29 14:01:56,290 - data.logger - INFO - 📝 Signal #25 logged: BUY @ 24539.87
2025-05-29 14:01:56,290 - data.historical_data - INFO - 📈 Historical BUY signal at 13:38 @ 24539.87
2025-05-29 14:01:56,290 - data.historical_data - INFO - Recovery complete: Found 25 historical crossovers
2025-05-29 14:01:56,290 - __main__ - INFO - ✅ Recovered 25 historical crossovers
2025-05-29 14:01:56,291 - utils.state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 14:01:56,291 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 14:01:56,292 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 14:01:56,292 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 14:01:56,292 - core.market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 14:01:56,293 - core.market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 14:01:56,293 - core.market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 14:01:58,523 - websocket - INFO - Websocket connected
2025-05-29 14:01:58,524 - core.market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 14:01:58,524 - core.market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 14:01:58,980 - core.market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 14:03:58,781 - __main__ - INFO - Data directory: data
2025-05-29 14:03:58,781 - __main__ - INFO - EMA Trading System initialized
2025-05-29 14:03:58,781 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 14:03:58,800 - utils.market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 14:03:58,801 - data.historical_database - INFO - Historical Database initialized: data/historical
2025-05-29 14:03:58,801 - core.ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 14:03:58,801 - data.logger - INFO - Loaded daily state: 66 signals, P&L: 0.00
2025-05-29 14:03:58,802 - data.logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 14:03:58,802 - data.logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 14:03:58,802 - core.strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 14:03:58,802 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 14:03:58,802 - core.market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 14:03:58,802 - __main__ - INFO - All components initialized successfully
2025-05-29 14:03:58,803 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 1h 11m
2025-05-29 14:03:58,803 - __main__ - INFO - 📊 Checking historical database...
2025-05-29 14:03:58,809 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:03:58,814 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:03:58,815 - __main__ - INFO - 📊 Historical database current: 14 days, 5040 candles
2025-05-29 14:03:58,815 - __main__ - INFO - 🧮 Initializing EMAs with historical data...
2025-05-29 14:03:58,820 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:03:58,821 - data.historical_database - INFO - Retrieved 3600 historical prices from 10 days
2025-05-29 14:03:58,821 - core.ema - INFO - Loading EMA state from 3600 historical prices for 1min
2025-05-29 14:03:58,821 - core.ema - INFO - Reset data for timeframe: 1min
2025-05-29 14:03:58,826 - core.ema - INFO - EMA state loaded for 1min: EMA10=23961.84, EMA5=23945.28
2025-05-29 14:03:58,826 - __main__ - INFO - ✅ EMAs initialized with 3600 historical prices
2025-05-29 14:03:58,826 - __main__ - INFO - 📈 Current EMAs: EMA10=23961.84, EMA5=23945.28
2025-05-29 14:03:58,826 - __main__ - INFO - 🔄 New trading day detected - but EMAs already initialized with historical data
2025-05-29 14:03:58,826 - data.logger - INFO - Daily state reset for new trading session
2025-05-29 14:03:58,826 - utils.state_manager - INFO - Daily state reset
2025-05-29 14:03:58,827 - data.historical_data - INFO - Market open for 4.8 hours with no signals - recovery needed
2025-05-29 14:03:58,827 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 14:03:58,827 - data.historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:03:58.827332+05:30
2025-05-29 14:03:58,827 - data.historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:03:58.827332+05:30
2025-05-29 14:04:00,259 - data.historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 14:04:00,260 - data.historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 14:04:00,260 - data.historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:03:58.827332+05:30
2025-05-29 14:04:00,267 - data.historical_data - INFO - Generated 289 mock historical candles
2025-05-29 14:04:00,268 - data.logger - INFO - 📝 Signal #1 logged: BUY @ 24735.80
2025-05-29 14:04:00,268 - data.historical_data - INFO - 📈 Historical BUY signal at 09:15 @ 24735.80
2025-05-29 14:04:00,269 - data.logger - INFO - 📝 Signal #2 logged: SELL @ 24818.03
2025-05-29 14:04:00,269 - data.historical_data - INFO - 📈 Historical SELL signal at 10:24 @ 24818.03
2025-05-29 14:04:00,270 - data.logger - INFO - 📝 Signal #3 logged: BUY @ 24615.54
2025-05-29 14:04:00,270 - data.historical_data - INFO - 📈 Historical BUY signal at 11:06 @ 24615.54
2025-05-29 14:04:00,271 - data.logger - INFO - 📝 Signal #4 logged: SELL @ 24572.39
2025-05-29 14:04:00,271 - data.historical_data - INFO - 📈 Historical SELL signal at 11:07 @ 24572.39
2025-05-29 14:04:00,271 - data.logger - INFO - 📝 Signal #5 logged: BUY @ 24561.47
2025-05-29 14:04:00,272 - data.historical_data - INFO - 📈 Historical BUY signal at 11:21 @ 24561.47
2025-05-29 14:04:00,272 - data.logger - INFO - 📝 Signal #6 logged: SELL @ 24471.55
2025-05-29 14:04:00,273 - data.historical_data - INFO - 📈 Historical SELL signal at 11:28 @ 24471.55
2025-05-29 14:04:00,274 - data.logger - INFO - 📝 Signal #7 logged: BUY @ 24203.83
2025-05-29 14:04:00,274 - data.historical_data - INFO - 📈 Historical BUY signal at 12:16 @ 24203.83
2025-05-29 14:04:00,274 - data.logger - INFO - 📝 Signal #8 logged: SELL @ 24200.50
2025-05-29 14:04:00,275 - data.historical_data - INFO - 📈 Historical SELL signal at 12:31 @ 24200.50
2025-05-29 14:04:00,275 - data.logger - INFO - 📝 Signal #9 logged: BUY @ 24265.81
2025-05-29 14:04:00,275 - data.historical_data - INFO - 📈 Historical BUY signal at 12:35 @ 24265.81
2025-05-29 14:04:00,276 - data.logger - INFO - 📝 Signal #10 logged: SELL @ 24250.55
2025-05-29 14:04:00,276 - data.historical_data - INFO - 📈 Historical SELL signal at 12:41 @ 24250.55
2025-05-29 14:04:00,276 - data.logger - INFO - 📝 Signal #11 logged: BUY @ 24273.39
2025-05-29 14:04:00,277 - data.historical_data - INFO - 📈 Historical BUY signal at 12:43 @ 24273.39
2025-05-29 14:04:00,277 - data.logger - INFO - 📝 Signal #12 logged: SELL @ 24237.74
2025-05-29 14:04:00,278 - data.historical_data - INFO - 📈 Historical SELL signal at 12:47 @ 24237.74
2025-05-29 14:04:00,278 - data.logger - INFO - 📝 Signal #13 logged: BUY @ 24283.60
2025-05-29 14:04:00,279 - data.historical_data - INFO - 📈 Historical BUY signal at 12:49 @ 24283.60
2025-05-29 14:04:00,279 - data.logger - INFO - 📝 Signal #14 logged: SELL @ 24341.85
2025-05-29 14:04:00,280 - data.historical_data - INFO - 📈 Historical SELL signal at 13:08 @ 24341.85
2025-05-29 14:04:00,280 - data.logger - INFO - 📝 Signal #15 logged: BUY @ 24383.53
2025-05-29 14:04:00,280 - data.historical_data - INFO - 📈 Historical BUY signal at 13:14 @ 24383.53
2025-05-29 14:04:00,281 - data.logger - INFO - 📝 Signal #16 logged: SELL @ 24343.94
2025-05-29 14:04:00,281 - data.historical_data - INFO - 📈 Historical SELL signal at 13:19 @ 24343.94
2025-05-29 14:04:00,282 - data.logger - INFO - 📝 Signal #17 logged: BUY @ 24376.00
2025-05-29 14:04:00,282 - data.historical_data - INFO - 📈 Historical BUY signal at 13:26 @ 24376.00
2025-05-29 14:04:00,283 - data.logger - INFO - 📝 Signal #18 logged: SELL @ 24335.66
2025-05-29 14:04:00,283 - data.historical_data - INFO - 📈 Historical SELL signal at 13:34 @ 24335.66
2025-05-29 14:04:00,284 - data.logger - INFO - 📝 Signal #19 logged: BUY @ 24381.20
2025-05-29 14:04:00,284 - data.historical_data - INFO - 📈 Historical BUY signal at 13:35 @ 24381.20
2025-05-29 14:04:00,285 - data.logger - INFO - 📝 Signal #20 logged: SELL @ 24335.07
2025-05-29 14:04:00,285 - data.historical_data - INFO - 📈 Historical SELL signal at 13:41 @ 24335.07
2025-05-29 14:04:00,286 - data.logger - INFO - 📝 Signal #21 logged: BUY @ 24162.60
2025-05-29 14:04:00,286 - data.historical_data - INFO - 📈 Historical BUY signal at 14:03 @ 24162.60
2025-05-29 14:04:00,286 - data.historical_data - INFO - Recovery complete: Found 21 historical crossovers
2025-05-29 14:04:00,286 - __main__ - INFO - ✅ Recovered 21 historical crossovers
2025-05-29 14:04:00,287 - utils.state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 14:04:00,287 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 14:04:00,288 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 14:04:00,289 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 14:04:00,289 - core.market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 14:04:00,289 - core.market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 14:04:00,289 - core.market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 14:04:01,043 - websocket - INFO - Websocket connected
2025-05-29 14:04:01,044 - core.market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 14:04:01,045 - core.market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 14:04:02,032 - core.market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 14:04:27,606 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 14:04:27,606 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 14:04:27,606 - core.market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 14:04:27,682 - core.market_feed - WARNING - WebSocket closed: None - None
2025-05-29 14:04:27,685 - core.market_feed - INFO - Market feed stopped
2025-05-29 14:04:27,686 - utils.state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 14:04:27,686 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 14:04:27,686 - data.logger - INFO - Closed daily CSV file for 20250529
2025-05-29 14:04:27,686 - __main__ - INFO - System stopped successfully
2025-05-29 14:05:11,053 - __main__ - INFO - Data directory: data
2025-05-29 14:05:11,053 - __main__ - INFO - EMA Trading System initialized
2025-05-29 14:05:11,053 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 14:05:11,068 - utils.market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 14:05:11,068 - data.historical_database - INFO - Historical Database initialized: data/historical
2025-05-29 14:05:11,068 - core.ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 14:05:11,069 - data.logger - INFO - Loaded daily state: 87 signals, P&L: 0.00
2025-05-29 14:05:11,069 - data.logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 14:05:11,069 - data.logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 14:05:11,069 - core.strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 14:05:11,069 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 14:05:11,070 - core.market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 14:05:11,070 - __main__ - INFO - All components initialized successfully
2025-05-29 14:05:11,070 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 1h 9m
2025-05-29 14:05:11,070 - __main__ - INFO - 📊 Checking historical database...
2025-05-29 14:05:11,077 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:05:11,083 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:05:11,084 - __main__ - INFO - 📊 Historical database current: 14 days, 5040 candles
2025-05-29 14:05:11,084 - __main__ - INFO - 🧮 Initializing EMAs with historical data...
2025-05-29 14:05:11,088 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:05:11,089 - data.historical_database - INFO - Retrieved 3600 historical prices from 10 days
2025-05-29 14:05:11,090 - core.ema - INFO - Loading EMA state from 3600 historical prices for 1min
2025-05-29 14:05:11,091 - core.ema - INFO - Reset data for timeframe: 1min
2025-05-29 14:05:11,096 - core.ema - INFO - EMA state loaded for 1min: EMA10=23961.84, EMA5=23945.28
2025-05-29 14:05:11,096 - __main__ - INFO - ✅ EMAs initialized with 3600 historical prices
2025-05-29 14:05:11,096 - __main__ - INFO - 📈 Current EMAs: EMA10=23961.84, EMA5=23945.28
2025-05-29 14:05:11,096 - __main__ - INFO - 🔄 New trading day detected - but EMAs already initialized with historical data
2025-05-29 14:05:11,096 - data.logger - INFO - Daily state reset for new trading session
2025-05-29 14:05:11,096 - utils.state_manager - INFO - Daily state reset
2025-05-29 14:05:11,096 - data.historical_data - INFO - Market open for 4.8 hours with no signals - recovery needed
2025-05-29 14:05:11,096 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 14:05:11,097 - data.historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:05:11.097001+05:30
2025-05-29 14:05:11,097 - data.historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:05:11.097001+05:30
2025-05-29 14:05:11,275 - data.historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 14:05:11,276 - data.historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 14:05:11,277 - data.historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:05:11.097001+05:30
2025-05-29 14:05:11,284 - data.historical_data - INFO - Generated 291 mock historical candles
2025-05-29 14:05:11,284 - data.logger - INFO - 📝 Signal #1 logged: BUY @ 24726.83
2025-05-29 14:05:11,285 - data.historical_data - INFO - 📈 Historical BUY signal at 09:15 @ 24726.83
2025-05-29 14:05:11,286 - data.logger - INFO - 📝 Signal #2 logged: SELL @ 24839.92
2025-05-29 14:05:11,286 - data.historical_data - INFO - 📈 Historical SELL signal at 09:43 @ 24839.92
2025-05-29 14:05:11,287 - data.logger - INFO - 📝 Signal #3 logged: BUY @ 24879.45
2025-05-29 14:05:11,287 - data.historical_data - INFO - 📈 Historical BUY signal at 09:46 @ 24879.45
2025-05-29 14:05:11,287 - data.logger - INFO - 📝 Signal #4 logged: SELL @ 24805.87
2025-05-29 14:05:11,288 - data.historical_data - INFO - 📈 Historical SELL signal at 09:53 @ 24805.87
2025-05-29 14:05:11,288 - data.logger - INFO - 📝 Signal #5 logged: BUY @ 24784.01
2025-05-29 14:05:11,289 - data.historical_data - INFO - 📈 Historical BUY signal at 10:05 @ 24784.01
2025-05-29 14:05:11,289 - data.logger - INFO - 📝 Signal #6 logged: SELL @ 24787.68
2025-05-29 14:05:11,290 - data.historical_data - INFO - 📈 Historical SELL signal at 10:18 @ 24787.68
2025-05-29 14:05:11,290 - data.logger - INFO - 📝 Signal #7 logged: BUY @ 24768.65
2025-05-29 14:05:11,290 - data.historical_data - INFO - 📈 Historical BUY signal at 10:29 @ 24768.65
2025-05-29 14:05:11,291 - data.logger - INFO - 📝 Signal #8 logged: SELL @ 24743.02
2025-05-29 14:05:11,291 - data.historical_data - INFO - 📈 Historical SELL signal at 10:40 @ 24743.02
2025-05-29 14:05:11,292 - data.logger - INFO - 📝 Signal #9 logged: BUY @ 24659.14
2025-05-29 14:05:11,292 - data.historical_data - INFO - 📈 Historical BUY signal at 11:11 @ 24659.14
2025-05-29 14:05:11,293 - data.logger - INFO - 📝 Signal #10 logged: SELL @ 24597.94
2025-05-29 14:05:11,293 - data.historical_data - INFO - 📈 Historical SELL signal at 11:17 @ 24597.94
2025-05-29 14:05:11,294 - data.logger - INFO - 📝 Signal #11 logged: BUY @ 24581.30
2025-05-29 14:05:11,294 - data.historical_data - INFO - 📈 Historical BUY signal at 11:29 @ 24581.30
2025-05-29 14:05:11,295 - data.logger - INFO - 📝 Signal #12 logged: SELL @ 24705.90
2025-05-29 14:05:11,295 - data.historical_data - INFO - 📈 Historical SELL signal at 12:04 @ 24705.90
2025-05-29 14:05:11,295 - data.logger - INFO - 📝 Signal #13 logged: BUY @ 24789.57
2025-05-29 14:05:11,296 - data.historical_data - INFO - 📈 Historical BUY signal at 12:06 @ 24789.57
2025-05-29 14:05:11,296 - data.logger - INFO - 📝 Signal #14 logged: SELL @ 24748.30
2025-05-29 14:05:11,296 - data.historical_data - INFO - 📈 Historical SELL signal at 12:10 @ 24748.30
2025-05-29 14:05:11,296 - data.logger - INFO - 📝 Signal #15 logged: BUY @ 24775.60
2025-05-29 14:05:11,297 - data.historical_data - INFO - 📈 Historical BUY signal at 12:11 @ 24775.60
2025-05-29 14:05:11,297 - data.logger - INFO - 📝 Signal #16 logged: SELL @ 24737.61
2025-05-29 14:05:11,297 - data.historical_data - INFO - 📈 Historical SELL signal at 12:12 @ 24737.61
2025-05-29 14:05:11,298 - data.logger - INFO - 📝 Signal #17 logged: BUY @ 24777.21
2025-05-29 14:05:11,298 - data.historical_data - INFO - 📈 Historical BUY signal at 12:14 @ 24777.21
2025-05-29 14:05:11,298 - data.logger - INFO - 📝 Signal #18 logged: SELL @ 24733.16
2025-05-29 14:05:11,299 - data.historical_data - INFO - 📈 Historical SELL signal at 12:15 @ 24733.16
2025-05-29 14:05:11,299 - data.logger - INFO - 📝 Signal #19 logged: BUY @ 24797.01
2025-05-29 14:05:11,299 - data.historical_data - INFO - 📈 Historical BUY signal at 12:22 @ 24797.01
2025-05-29 14:05:11,300 - data.logger - INFO - 📝 Signal #20 logged: SELL @ 24807.43
2025-05-29 14:05:11,300 - data.historical_data - INFO - 📈 Historical SELL signal at 12:35 @ 24807.43
2025-05-29 14:05:11,301 - data.logger - INFO - 📝 Signal #21 logged: BUY @ 24850.89
2025-05-29 14:05:11,301 - data.historical_data - INFO - 📈 Historical BUY signal at 12:40 @ 24850.89
2025-05-29 14:05:11,302 - data.logger - INFO - 📝 Signal #22 logged: SELL @ 24776.92
2025-05-29 14:05:11,302 - data.historical_data - INFO - 📈 Historical SELL signal at 12:43 @ 24776.92
2025-05-29 14:05:11,303 - data.logger - INFO - 📝 Signal #23 logged: BUY @ 24793.43
2025-05-29 14:05:11,303 - data.historical_data - INFO - 📈 Historical BUY signal at 12:52 @ 24793.43
2025-05-29 14:05:11,303 - data.logger - INFO - 📝 Signal #24 logged: SELL @ 24770.20
2025-05-29 14:05:11,304 - data.historical_data - INFO - 📈 Historical SELL signal at 12:58 @ 24770.20
2025-05-29 14:05:11,304 - data.logger - INFO - 📝 Signal #25 logged: BUY @ 24818.31
2025-05-29 14:05:11,304 - data.historical_data - INFO - 📈 Historical BUY signal at 12:59 @ 24818.31
2025-05-29 14:05:11,305 - data.logger - INFO - 📝 Signal #26 logged: SELL @ 24958.91
2025-05-29 14:05:11,305 - data.historical_data - INFO - 📈 Historical SELL signal at 13:20 @ 24958.91
2025-05-29 14:05:11,306 - data.logger - INFO - 📝 Signal #27 logged: BUY @ 24794.61
2025-05-29 14:05:11,306 - data.historical_data - INFO - 📈 Historical BUY signal at 13:40 @ 24794.61
2025-05-29 14:05:11,307 - data.historical_data - INFO - Recovery complete: Found 27 historical crossovers
2025-05-29 14:05:11,307 - __main__ - INFO - ✅ Recovered 27 historical crossovers
2025-05-29 14:05:11,308 - utils.state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 14:05:11,309 - __main__ - INFO - 🚀 System started in FOREGROUND mode. Press Ctrl+C to stop.
2025-05-29 14:05:11,309 - core.market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 14:05:11,309 - core.market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 14:05:11,309 - core.market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 14:05:11,686 - websocket - INFO - Websocket connected
2025-05-29 14:05:11,687 - core.market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 14:05:11,688 - core.market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 14:05:11,742 - core.market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 14:05:36,641 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 14:05:36,641 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 14:05:36,642 - core.market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 14:05:36,684 - core.market_feed - WARNING - WebSocket closed: None - None
2025-05-29 14:05:36,686 - core.market_feed - INFO - Market feed stopped
2025-05-29 14:05:36,686 - utils.state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 14:05:36,686 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 14:05:36,687 - data.logger - INFO - Closed daily CSV file for 20250529
2025-05-29 14:05:36,687 - __main__ - INFO - System stopped successfully
2025-05-29 14:07:00,627 - data.logger - INFO - 📝 Signal #26 logged: SELL @ 24731.95
2025-05-29 14:07:00,628 - core.strategy - INFO - 🔔 SELL Signal: 1min 5_10 @ 24731.95 (EMA5: 24737.97, EMA10: 24739.01) P&L: 0.00
2025-05-29 14:21:01,475 - data.logger - INFO - 📝 Signal #27 logged: BUY @ 24734.90
2025-05-29 14:21:01,476 - core.strategy - INFO - 🔔 BUY Signal: 1min 5_10 @ 24734.90 (EMA5: 24730.73, EMA10: 24729.95) P&L: -2.95
