2025-05-29 11:10:55,555 - __main__ - INFO - Data directory: data
2025-05-29 11:10:55,555 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:10:55,555 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:10:55,556 - __main__ - WARNING - DhanHQ credentials not configured - will use Mock Market Feed for testing
2025-05-29 11:10:55,556 - __main__ - INFO - To use live data, update your credentials in config/config.json
2025-05-29 11:10:55,556 - __main__ - INFO - Get your credentials from: https://web.dhan.co -> My Profile -> Access DhanHQ APIs
2025-05-29 11:10:55,556 - ema - INFO - EMA Calculator initialized with periods: [5, 8, 10, 12, 21, 26]
2025-05-29 11:10:55,556 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_111055.csv
2025-05-29 11:10:55,556 - logger - INFO - Created CSV file for 5min: data/nifty50_ema_signals_5min_20250529_111055.csv
2025-05-29 11:10:55,556 - logger - INFO - Created CSV file for 10min: data/nifty50_ema_signals_10min_20250529_111055.csv
2025-05-29 11:10:55,556 - logger - INFO - Signal Logger initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:10:55,556 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:10:55,556 - __main__ - INFO - Using Mock Market Feed for testing
2025-05-29 11:10:55,556 - market_feed - INFO - Mock Market Feed initialized for testing
2025-05-29 11:10:55,556 - __main__ - INFO - All components initialized successfully
2025-05-29 11:10:55,556 - __main__ - INFO - System started successfully. Press Ctrl+C to stop.
2025-05-29 11:10:55,556 - market_feed - INFO - Starting mock market feed...
2025-05-29 11:11:10,125 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 11:11:10,125 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 11:11:10,125 - market_feed - INFO - Stopping mock market feed...
2025-05-29 11:11:10,125 - logger - INFO - Closed CSV file for 1min
2025-05-29 11:11:10,125 - logger - INFO - Closed CSV file for 5min
2025-05-29 11:11:10,126 - logger - INFO - Closed CSV file for 10min
2025-05-29 11:11:10,126 - __main__ - INFO - System stopped successfully
2025-05-29 11:11:10,246 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 11:19:22,882 - __main__ - INFO - Data directory: data
2025-05-29 11:19:22,882 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:19:22,882 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:19:22,882 - ema - INFO - EMA Calculator initialized with periods: [5, 8, 10, 12, 21, 26]
2025-05-29 11:19:22,882 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_111922.csv
2025-05-29 11:19:22,883 - logger - INFO - Created CSV file for 5min: data/nifty50_ema_signals_5min_20250529_111922.csv
2025-05-29 11:19:22,883 - logger - INFO - Created CSV file for 10min: data/nifty50_ema_signals_10min_20250529_111922.csv
2025-05-29 11:19:22,883 - logger - INFO - Signal Logger initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:19:22,883 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:19:22,883 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 11:19:22,883 - __main__ - ERROR - Error initializing components: DhanHQ and websocket libraries are required for live market feed. Install with: pip install dhanhq websocket-client
2025-05-29 11:19:48,211 - __main__ - INFO - Data directory: data
2025-05-29 11:19:48,211 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:19:48,211 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:19:48,211 - ema - INFO - EMA Calculator initialized with periods: [5, 8, 10, 12, 21, 26]
2025-05-29 11:19:48,212 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_111948.csv
2025-05-29 11:19:48,212 - logger - INFO - Created CSV file for 5min: data/nifty50_ema_signals_5min_20250529_111948.csv
2025-05-29 11:19:48,212 - logger - INFO - Created CSV file for 10min: data/nifty50_ema_signals_10min_20250529_111948.csv
2025-05-29 11:19:48,212 - logger - INFO - Signal Logger initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:19:48,212 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min', '5min', '10min']
2025-05-29 11:19:48,212 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 11:19:48,212 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 11:19:48,212 - __main__ - INFO - All components initialized successfully
2025-05-29 11:19:48,212 - __main__ - INFO - System started successfully. Press Ctrl+C to stop.
2025-05-29 11:19:48,212 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 11:19:48,212 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:19:48,212 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:19:48,486 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:48 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:19:48,486 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:48 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:19:48,486 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:19:48,486 - market_feed - INFO - Scheduling reconnection attempt 1 in 5 seconds...
2025-05-29 11:19:53,487 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:19:53,665 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:53 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:19:53,665 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:53 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:19:53,666 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:19:53,666 - market_feed - INFO - Scheduling reconnection attempt 2 in 5 seconds...
2025-05-29 11:19:58,667 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:19:58,963 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:58 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:19:58,964 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:49:58 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:19:58,964 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:19:58,964 - market_feed - INFO - Scheduling reconnection attempt 3 in 5 seconds...
2025-05-29 11:20:03,965 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:04,137 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:04 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:04,137 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:04 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:04,137 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:04,138 - market_feed - INFO - Scheduling reconnection attempt 4 in 5 seconds...
2025-05-29 11:20:09,138 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:10,589 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:10 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:10,589 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:10 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:10,589 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:10,589 - market_feed - INFO - Scheduling reconnection attempt 5 in 5 seconds...
2025-05-29 11:20:15,591 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:15,928 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:15 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:15,929 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:15 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:15,929 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:15,929 - market_feed - INFO - Scheduling reconnection attempt 6 in 5 seconds...
2025-05-29 11:20:20,930 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:21,117 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:21 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:21,118 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:21 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:21,118 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:21,118 - market_feed - INFO - Scheduling reconnection attempt 7 in 5 seconds...
2025-05-29 11:20:26,119 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:26,310 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:26 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:26,310 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:26 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:26,310 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:26,311 - market_feed - INFO - Scheduling reconnection attempt 8 in 5 seconds...
2025-05-29 11:20:31,311 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:31,593 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:31 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:31,593 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:31 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:31,593 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:31,594 - market_feed - INFO - Scheduling reconnection attempt 9 in 5 seconds...
2025-05-29 11:20:36,594 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:36,759 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:36 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:36,759 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:36 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:36,759 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:20:36,759 - market_feed - INFO - Scheduling reconnection attempt 10 in 5 seconds...
2025-05-29 11:20:41,760 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:20:42,181 - market_feed - ERROR - WebSocket error: Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:42 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None
2025-05-29 11:20:42,181 - websocket - ERROR - Handshake status 200 None -+-+- {'date': 'Thu, 29 May 2025 05:50:42 GMT', 'content-type': 'text/html;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Access-Control-Request-Headers', 'content-language': 'en-US'} -+-+- None - goodbye
2025-05-29 11:20:42,181 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:38:52,723 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 11:38:52,723 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 11:38:52,723 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 11:38:52,723 - market_feed - INFO - Market feed stopped
2025-05-29 11:38:52,723 - logger - INFO - Closed CSV file for 1min
2025-05-29 11:38:52,723 - logger - INFO - Closed CSV file for 5min
2025-05-29 11:38:52,724 - logger - INFO - Closed CSV file for 10min
2025-05-29 11:38:52,724 - __main__ - INFO - System stopped successfully
2025-05-29 11:46:16,974 - __main__ - INFO - Data directory: data
2025-05-29 11:46:16,974 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:46:16,975 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:46:16,975 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 11:46:16,975 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_114616.csv
2025-05-29 11:46:16,975 - logger - INFO - Signal Logger initialized for timeframes: ['1min']
2025-05-29 11:46:16,975 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 11:46:16,975 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 11:46:16,975 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 11:46:16,975 - __main__ - INFO - All components initialized successfully
2025-05-29 11:46:16,975 - __main__ - INFO - System started successfully. Press Ctrl+C to stop.
2025-05-29 11:46:16,975 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 11:46:16,976 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:46:16,976 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:46:17,306 - websocket - INFO - Websocket connected
2025-05-29 11:46:17,307 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 11:46:17,308 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:46:17,340 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 11:46:46,976 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:46:51,977 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:46:56,978 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:01,979 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:06,979 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:11,980 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:16,981 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:21,981 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:26,982 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:31,982 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:36,983 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:41,984 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:46,984 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:51,985 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:47:56,985 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:01,986 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:06,986 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:11,987 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:16,987 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:21,988 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:26,988 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:31,989 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:36,990 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:41,990 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:46,990 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:51,991 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:48:56,992 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:01,993 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:06,993 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:11,994 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:16,994 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:21,995 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:26,996 - market_feed - ERROR - Heartbeat error: 'WebSocketApp' object has no attribute 'ping'
2025-05-29 11:49:31,289 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-05-29 11:49:31,290 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 11:49:31,290 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 11:49:31,324 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 11:49:31,325 - market_feed - INFO - Market feed stopped
2025-05-29 11:49:31,325 - logger - INFO - Closed CSV file for 1min
2025-05-29 11:49:31,325 - __main__ - INFO - System stopped successfully
2025-05-29 11:54:11,557 - __main__ - INFO - Data directory: data
2025-05-29 11:54:11,558 - __main__ - INFO - EMA Trading System initialized
2025-05-29 11:54:11,558 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 11:54:11,558 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 11:54:11,558 - logger - INFO - Created CSV file for 1min: data/nifty50_ema_signals_1min_20250529_115411.csv
2025-05-29 11:54:11,558 - logger - INFO - Signal Logger initialized for timeframes: ['1min']
2025-05-29 11:54:11,559 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 11:54:11,559 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 11:54:11,559 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 11:54:11,559 - __main__ - INFO - All components initialized successfully
2025-05-29 11:54:11,559 - __main__ - INFO - System started successfully. Press Ctrl+C to stop.
2025-05-29 11:54:11,559 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 11:54:11,559 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:54:11,559 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 11:54:12,013 - websocket - INFO - Websocket connected
2025-05-29 11:54:12,014 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 11:54:12,015 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 11:54:12,043 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0


2025-05-29 12:09:00,339 - strategy - INFO - 🔔 SELL Signal: 1min 5_10 @ 24737.70 (EMA5: 24739.01, EMA10: 24739.20) P&L: 0.00
