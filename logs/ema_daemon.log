2025-05-29 12:27:55,994 - __main__ - INFO - Data directory: data
2025-05-29 12:27:55,995 - __main__ - INFO - EMA Trading System initialized
2025-05-29 12:27:55,995 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 12:27:56,005 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 12:27:56,005 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 12:27:56,005 - logger - INFO - Created new daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 12:27:56,005 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 12:27:56,005 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 12:27:56,006 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 12:27:56,006 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 12:27:56,006 - __main__ - INFO - All components initialized successfully
2025-05-29 12:27:56,006 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 47m
2025-05-29 12:27:56,006 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 12:27:56,006 - ema - INFO - Reset all EMA calculator data
2025-05-29 12:27:56,006 - logger - INFO - Daily state reset for new trading session
2025-05-29 12:27:56,006 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 12:27:56,006 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 12:27:56,006 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 12:27:56,006 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 12:27:56,006 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:27:56,006 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 12:27:56,310 - websocket - INFO - Websocket connected
2025-05-29 12:27:56,311 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 12:27:56,312 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:27:56,343 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 12:46:01,533 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 12:46:01,533 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 12:46:01,533 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 12:46:01,562 - market_feed - INFO - Market feed stopped
2025-05-29 12:46:01,562 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 12:46:01,563 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 12:46:01,563 - __main__ - INFO - System stopped successfully
2025-05-29 12:48:44,441 - __main__ - INFO - Data directory: data
2025-05-29 12:48:44,441 - __main__ - INFO - EMA Trading System initialized
2025-05-29 12:48:44,441 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 12:48:44,452 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 12:48:44,452 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 12:48:44,452 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 12:48:44,452 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 12:48:44,452 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 12:48:44,452 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 12:48:44,452 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 12:48:44,452 - __main__ - INFO - All components initialized successfully
2025-05-29 12:48:44,453 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 26m
2025-05-29 12:48:44,453 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 12:48:44,453 - ema - INFO - Reset all EMA calculator data
2025-05-29 12:48:44,453 - logger - INFO - Daily state reset for new trading session
2025-05-29 12:48:44,453 - state_manager - INFO - Daily state reset
2025-05-29 12:48:44,453 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 12:48:44,453 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 12:48:44,453 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 12:48:44,453 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 12:48:44,453 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:48:44,453 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 12:48:44,708 - websocket - INFO - Websocket connected
2025-05-29 12:48:44,709 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 12:48:44,709 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:48:44,738 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 13:00:59,878 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 13:00:59,878 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 13:00:59,879 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 13:00:59,906 - market_feed - INFO - Market feed stopped
2025-05-29 13:00:59,906 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:00:59,906 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 13:00:59,906 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 13:00:59,906 - __main__ - INFO - System stopped successfully
2025-05-29 13:01:09,857 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 13:03:15,568 - __main__ - INFO - Data directory: data
2025-05-29 13:03:15,568 - __main__ - INFO - EMA Trading System initialized
2025-05-29 13:03:15,568 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 13:03:15,579 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 13:03:15,579 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 13:03:15,580 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 13:03:15,580 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 13:03:15,580 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 13:03:15,580 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 13:03:15,580 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 13:03:15,580 - __main__ - INFO - All components initialized successfully
2025-05-29 13:03:15,580 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 11m
2025-05-29 13:03:15,580 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 13:03:15,580 - ema - INFO - Reset all EMA calculator data
2025-05-29 13:03:15,580 - logger - INFO - Daily state reset for new trading session
2025-05-29 13:03:15,580 - state_manager - INFO - Daily state reset
2025-05-29 13:03:15,581 - historical_data - INFO - Market open for 3.8 hours with no signals - recovery needed
2025-05-29 13:03:15,581 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 13:03:15,581 - historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,581 - historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,896 - historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:03:15,897 - historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 13:03:15,897 - historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,898 - historical_data - INFO - Generated 229 mock historical candles
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #1 logged: SELL @ 24721.87
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 09:25 @ 24721.87
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #2 logged: BUY @ 24707.06
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical BUY signal at 09:38 @ 24707.06
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #3 logged: SELL @ 24787.47
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 10:04 @ 24787.47
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #4 logged: BUY @ 24833.09
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical BUY signal at 10:08 @ 24833.09
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #5 logged: SELL @ 24790.24
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 10:17 @ 24790.24
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #6 logged: BUY @ 24841.81
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 10:24 @ 24841.81
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #7 logged: SELL @ 25064.92
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 10:58 @ 25064.92
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #8 logged: BUY @ 25119.66
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:09 @ 25119.66
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #9 logged: SELL @ 25027.72
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 11:16 @ 25027.72
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #10 logged: BUY @ 25113.26
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:24 @ 25113.26
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #11 logged: SELL @ 25030.76
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 11:26 @ 25030.76
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #12 logged: BUY @ 25046.41
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:35 @ 25046.41
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #13 logged: SELL @ 25258.03
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:01 @ 25258.03
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #14 logged: BUY @ 25301.63
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:02 @ 25301.63
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #15 logged: SELL @ 25269.30
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:10 @ 25269.30
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #16 logged: BUY @ 25310.95
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:15 @ 25310.95
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #17 logged: SELL @ 25335.76
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:27 @ 25335.76
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #18 logged: BUY @ 25343.37
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:37 @ 25343.37
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #19 logged: SELL @ 25370.05
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:50 @ 25370.05
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #20 logged: BUY @ 25408.15
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:51 @ 25408.15
2025-05-29 13:03:15,901 - logger - INFO - 📝 Signal #21 logged: SELL @ 25395.60
2025-05-29 13:03:15,901 - historical_data - INFO - 📈 Historical SELL signal at 12:59 @ 25395.60
2025-05-29 13:03:15,901 - historical_data - INFO - Recovery complete: Found 21 historical crossovers
2025-05-29 13:03:15,901 - __main__ - INFO - ✅ Recovered 21 historical crossovers
2025-05-29 13:03:15,901 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:03:15,901 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 13:03:15,901 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 13:03:15,901 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 13:03:15,901 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 13:03:15,901 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:03:15,901 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 13:03:16,512 - websocket - INFO - Websocket connected
2025-05-29 13:03:16,513 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 13:03:16,514 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:03:16,545 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 13:21:13,459 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 13:21:13,459 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 13:21:13,459 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 13:21:13,488 - market_feed - INFO - Market feed stopped
2025-05-29 13:21:13,488 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 13:21:13,490 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:21:13,490 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 13:21:13,490 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 13:21:13,490 - __main__ - INFO - System stopped successfully
============================================================
Real-time EMA Crossover Logging System for NIFTY 50
============================================================
Mode: BACKGROUND
Config: config/config.json

2025-05-29 13:22:11,960 - __main__ - INFO - Data directory: data
2025-05-29 13:22:11,960 - __main__ - INFO - EMA Trading System initialized
2025-05-29 13:22:11,961 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 13:22:11,971 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 13:22:11,972 - historical_database - INFO - Historical Database initialized: data/historical
2025-05-29 13:22:11,972 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 13:22:11,972 - logger - INFO - Loaded daily state: 21 signals, P&L: 0.00
2025-05-29 13:22:11,972 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 13:22:11,972 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 13:22:11,972 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 13:22:11,972 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 13:22:11,972 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 13:22:11,972 - __main__ - INFO - All components initialized successfully
2025-05-29 13:22:11,973 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 1h 52m
2025-05-29 13:22:11,973 - __main__ - INFO - 📊 Checking historical database...
2025-05-29 13:22:11,973 - __main__ - INFO - 🔄 Updating 2-week historical database...
2025-05-29 13:22:11,973 - historical_database - INFO - Starting historical data update...
2025-05-29 13:22:11,973 - historical_database - INFO - No existing database found, starting fresh
2025-05-29 13:22:11,973 - historical_database - INFO - Fetching data for 2025-05-12
2025-05-29 13:22:12,682 - historical_database - ERROR - HTTP error 500 for 2025-05-12: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:12,682 - historical_database - WARNING - API failed for 2025-05-12, generating mock data
2025-05-29 13:22:12,683 - historical_database - INFO - Generating mock data for 2025-05-12
2025-05-29 13:22:12,698 - historical_database - INFO - Generated 360 mock candles for 2025-05-12
2025-05-29 13:22:12,698 - historical_database - INFO - Updated data for 2025-05-12: 360 candles
2025-05-29 13:22:13,199 - historical_database - INFO - Fetching data for 2025-05-13
2025-05-29 13:22:13,701 - historical_database - ERROR - HTTP error 500 for 2025-05-13: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:13,702 - historical_database - WARNING - API failed for 2025-05-13, generating mock data
2025-05-29 13:22:13,703 - historical_database - INFO - Generating mock data for 2025-05-13
2025-05-29 13:22:13,736 - historical_database - INFO - Generated 360 mock candles for 2025-05-13
2025-05-29 13:22:13,737 - historical_database - INFO - Updated data for 2025-05-13: 360 candles
2025-05-29 13:22:14,237 - historical_database - INFO - Fetching data for 2025-05-14
2025-05-29 13:22:14,398 - historical_database - ERROR - HTTP error 500 for 2025-05-14: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:14,398 - historical_database - WARNING - API failed for 2025-05-14, generating mock data
2025-05-29 13:22:14,398 - historical_database - INFO - Generating mock data for 2025-05-14
2025-05-29 13:22:14,416 - historical_database - INFO - Generated 360 mock candles for 2025-05-14
2025-05-29 13:22:14,416 - historical_database - INFO - Updated data for 2025-05-14: 360 candles
2025-05-29 13:22:14,917 - historical_database - INFO - Fetching data for 2025-05-15
2025-05-29 13:22:15,192 - historical_database - ERROR - HTTP error 500 for 2025-05-15: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:15,193 - historical_database - WARNING - API failed for 2025-05-15, generating mock data
2025-05-29 13:22:15,194 - historical_database - INFO - Generating mock data for 2025-05-15
2025-05-29 13:22:15,227 - historical_database - INFO - Generated 360 mock candles for 2025-05-15
2025-05-29 13:22:15,227 - historical_database - INFO - Updated data for 2025-05-15: 360 candles
2025-05-29 13:22:15,728 - historical_database - INFO - Fetching data for 2025-05-16
2025-05-29 13:22:16,697 - historical_database - ERROR - HTTP error 500 for 2025-05-16: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:16,697 - historical_database - WARNING - API failed for 2025-05-16, generating mock data
2025-05-29 13:22:16,698 - historical_database - INFO - Generating mock data for 2025-05-16
2025-05-29 13:22:16,731 - historical_database - INFO - Generated 360 mock candles for 2025-05-16
2025-05-29 13:22:16,731 - historical_database - INFO - Updated data for 2025-05-16: 360 candles
2025-05-29 13:22:17,232 - historical_database - INFO - Fetching data for 2025-05-19
2025-05-29 13:22:19,850 - historical_database - ERROR - HTTP error 500 for 2025-05-19: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:19,850 - historical_database - WARNING - API failed for 2025-05-19, generating mock data
2025-05-29 13:22:19,851 - historical_database - INFO - Generating mock data for 2025-05-19
2025-05-29 13:22:19,884 - historical_database - INFO - Generated 360 mock candles for 2025-05-19
2025-05-29 13:22:19,884 - historical_database - INFO - Updated data for 2025-05-19: 360 candles
2025-05-29 13:22:20,385 - historical_database - INFO - Fetching data for 2025-05-20
2025-05-29 13:22:21,268 - historical_database - ERROR - HTTP error 500 for 2025-05-20: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:21,269 - historical_database - WARNING - API failed for 2025-05-20, generating mock data
2025-05-29 13:22:21,269 - historical_database - INFO - Generating mock data for 2025-05-20
2025-05-29 13:22:21,277 - historical_database - INFO - Generated 360 mock candles for 2025-05-20
2025-05-29 13:22:21,277 - historical_database - INFO - Updated data for 2025-05-20: 360 candles
2025-05-29 13:22:21,777 - historical_database - INFO - Fetching data for 2025-05-21
2025-05-29 13:22:22,043 - historical_database - ERROR - HTTP error 500 for 2025-05-21: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:22,043 - historical_database - WARNING - API failed for 2025-05-21, generating mock data
2025-05-29 13:22:22,044 - historical_database - INFO - Generating mock data for 2025-05-21
2025-05-29 13:22:22,057 - historical_database - INFO - Generated 360 mock candles for 2025-05-21
2025-05-29 13:22:22,057 - historical_database - INFO - Updated data for 2025-05-21: 360 candles
2025-05-29 13:22:22,557 - historical_database - INFO - Fetching data for 2025-05-22
2025-05-29 13:22:23,307 - historical_database - ERROR - HTTP error 500 for 2025-05-22: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:23,308 - historical_database - WARNING - API failed for 2025-05-22, generating mock data
2025-05-29 13:22:23,308 - historical_database - INFO - Generating mock data for 2025-05-22
2025-05-29 13:22:23,337 - historical_database - INFO - Generated 360 mock candles for 2025-05-22
2025-05-29 13:22:23,337 - historical_database - INFO - Updated data for 2025-05-22: 360 candles
2025-05-29 13:22:23,838 - historical_database - INFO - Fetching data for 2025-05-23
2025-05-29 13:22:24,357 - historical_database - ERROR - HTTP error 500 for 2025-05-23: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:24,358 - historical_database - WARNING - API failed for 2025-05-23, generating mock data
2025-05-29 13:22:24,358 - historical_database - INFO - Generating mock data for 2025-05-23
2025-05-29 13:22:24,390 - historical_database - INFO - Generated 360 mock candles for 2025-05-23
2025-05-29 13:22:24,390 - historical_database - INFO - Updated data for 2025-05-23: 360 candles
2025-05-29 13:22:24,891 - historical_database - INFO - Fetching data for 2025-05-26
2025-05-29 13:22:25,069 - historical_database - ERROR - HTTP error 500 for 2025-05-26: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:25,071 - historical_database - WARNING - API failed for 2025-05-26, generating mock data
2025-05-29 13:22:25,071 - historical_database - INFO - Generating mock data for 2025-05-26
2025-05-29 13:22:25,101 - historical_database - INFO - Generated 360 mock candles for 2025-05-26
2025-05-29 13:22:25,101 - historical_database - INFO - Updated data for 2025-05-26: 360 candles
2025-05-29 13:22:25,602 - historical_database - INFO - Fetching data for 2025-05-27
2025-05-29 13:22:25,934 - historical_database - ERROR - HTTP error 500 for 2025-05-27: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:25,935 - historical_database - WARNING - API failed for 2025-05-27, generating mock data
2025-05-29 13:22:25,935 - historical_database - INFO - Generating mock data for 2025-05-27
2025-05-29 13:22:25,968 - historical_database - INFO - Generated 360 mock candles for 2025-05-27
2025-05-29 13:22:25,968 - historical_database - INFO - Updated data for 2025-05-27: 360 candles
2025-05-29 13:22:26,469 - historical_database - INFO - Fetching data for 2025-05-28
2025-05-29 13:22:26,647 - historical_database - ERROR - HTTP error 500 for 2025-05-28: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:26,648 - historical_database - WARNING - API failed for 2025-05-28, generating mock data
2025-05-29 13:22:26,649 - historical_database - INFO - Generating mock data for 2025-05-28
2025-05-29 13:22:26,683 - historical_database - INFO - Generated 360 mock candles for 2025-05-28
2025-05-29 13:22:26,683 - historical_database - INFO - Updated data for 2025-05-28: 360 candles
2025-05-29 13:22:27,184 - historical_database - INFO - Fetching data for 2025-05-29
2025-05-29 13:22:27,753 - historical_database - ERROR - HTTP error 500 for 2025-05-29: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:27,754 - historical_database - WARNING - API failed for 2025-05-29, generating mock data
2025-05-29 13:22:27,755 - historical_database - INFO - Generating mock data for 2025-05-29
2025-05-29 13:22:27,785 - historical_database - INFO - Generated 360 mock candles for 2025-05-29
2025-05-29 13:22:27,785 - historical_database - INFO - Updated data for 2025-05-29: 360 candles
2025-05-29 13:22:28,321 - historical_database - INFO - Saved database with 14 days
2025-05-29 13:22:28,322 - historical_database - INFO - Historical data update complete: 14 days updated
2025-05-29 13:22:28,329 - historical_database - INFO - Loaded historical database with 14 days
2025-05-29 13:22:28,330 - __main__ - INFO - ✅ Historical database updated: 14 days, 5040 candles
2025-05-29 13:22:28,330 - __main__ - INFO - 🧮 Initializing EMAs with historical data...
2025-05-29 13:22:28,333 - historical_database - INFO - Loaded historical database with 14 days
2025-05-29 13:22:28,333 - historical_database - INFO - Retrieved 3600 historical prices from 10 days
2025-05-29 13:22:28,334 - ema - INFO - Loading EMA state from 3600 historical prices for 1min
2025-05-29 13:22:28,334 - ema - INFO - Reset data for timeframe: 1min
2025-05-29 13:22:28,338 - ema - INFO - EMA state loaded for 1min: EMA10=23961.84, EMA5=23945.28
2025-05-29 13:22:28,338 - __main__ - INFO - ✅ EMAs initialized with 3600 historical prices
2025-05-29 13:22:28,338 - __main__ - INFO - 📈 Current EMAs: EMA10=23961.84, EMA5=23945.28
2025-05-29 13:22:28,339 - __main__ - INFO - 🔄 New trading day detected - but EMAs already initialized with historical data
2025-05-29 13:22:28,339 - logger - INFO - Daily state reset for new trading session
2025-05-29 13:22:28,339 - state_manager - INFO - Daily state reset
2025-05-29 13:22:28,339 - historical_data - INFO - Market open for 4.1 hours with no signals - recovery needed
2025-05-29 13:22:28,339 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 13:22:28,339 - historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:22:28.339386+05:30
2025-05-29 13:22:28,339 - historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:22:28.339386+05:30
2025-05-29 13:22:28,635 - historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:22:28,636 - historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 13:22:28,636 - historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:22:28.339386+05:30
2025-05-29 13:22:28,640 - historical_data - INFO - Generated 248 mock historical candles
2025-05-29 13:22:28,641 - logger - INFO - 📝 Signal #1 logged: BUY @ 24761.35
2025-05-29 13:22:28,641 - historical_data - INFO - 📈 Historical BUY signal at 09:15 @ 24761.35
2025-05-29 13:22:28,642 - logger - INFO - 📝 Signal #2 logged: SELL @ 24843.32
2025-05-29 13:22:28,642 - historical_data - INFO - 📈 Historical SELL signal at 09:43 @ 24843.32
2025-05-29 13:22:28,642 - logger - INFO - 📝 Signal #3 logged: BUY @ 24877.44
2025-05-29 13:22:28,643 - historical_data - INFO - 📈 Historical BUY signal at 09:52 @ 24877.44
2025-05-29 13:22:28,643 - logger - INFO - 📝 Signal #4 logged: SELL @ 24888.64
2025-05-29 13:22:28,643 - historical_data - INFO - 📈 Historical SELL signal at 10:13 @ 24888.64
2025-05-29 13:22:28,644 - logger - INFO - 📝 Signal #5 logged: BUY @ 24945.07
2025-05-29 13:22:28,644 - historical_data - INFO - 📈 Historical BUY signal at 10:15 @ 24945.07
2025-05-29 13:22:28,644 - logger - INFO - 📝 Signal #6 logged: SELL @ 24926.35
2025-05-29 13:22:28,644 - historical_data - INFO - 📈 Historical SELL signal at 10:23 @ 24926.35
2025-05-29 13:22:28,645 - logger - INFO - 📝 Signal #7 logged: BUY @ 24686.89
2025-05-29 13:22:28,645 - historical_data - INFO - 📈 Historical BUY signal at 11:02 @ 24686.89
2025-05-29 13:22:28,646 - logger - INFO - 📝 Signal #8 logged: SELL @ 24746.32
2025-05-29 13:22:28,646 - historical_data - INFO - 📈 Historical SELL signal at 11:24 @ 24746.32
2025-05-29 13:22:28,646 - logger - INFO - 📝 Signal #9 logged: BUY @ 24785.50
2025-05-29 13:22:28,646 - historical_data - INFO - 📈 Historical BUY signal at 11:25 @ 24785.50
2025-05-29 13:22:28,647 - logger - INFO - 📝 Signal #10 logged: SELL @ 24773.70
2025-05-29 13:22:28,647 - historical_data - INFO - 📈 Historical SELL signal at 11:33 @ 24773.70
2025-05-29 13:22:28,647 - logger - INFO - 📝 Signal #11 logged: BUY @ 24791.74
2025-05-29 13:22:28,648 - historical_data - INFO - 📈 Historical BUY signal at 11:34 @ 24791.74
2025-05-29 13:22:28,648 - logger - INFO - 📝 Signal #12 logged: SELL @ 24779.43
2025-05-29 13:22:28,648 - historical_data - INFO - 📈 Historical SELL signal at 11:35 @ 24779.43
2025-05-29 13:22:28,648 - logger - INFO - 📝 Signal #13 logged: BUY @ 24792.85
2025-05-29 13:22:28,649 - historical_data - INFO - 📈 Historical BUY signal at 11:36 @ 24792.85
2025-05-29 13:22:28,649 - logger - INFO - 📝 Signal #14 logged: SELL @ 24932.40
2025-05-29 13:22:28,649 - historical_data - INFO - 📈 Historical SELL signal at 11:57 @ 24932.40
2025-05-29 13:22:28,650 - logger - INFO - 📝 Signal #15 logged: BUY @ 24905.23
2025-05-29 13:22:28,650 - historical_data - INFO - 📈 Historical BUY signal at 12:11 @ 24905.23
2025-05-29 13:22:28,650 - logger - INFO - 📝 Signal #16 logged: SELL @ 25006.33
2025-05-29 13:22:28,651 - historical_data - INFO - 📈 Historical SELL signal at 12:29 @ 25006.33
2025-05-29 13:22:28,651 - logger - INFO - 📝 Signal #17 logged: BUY @ 25075.28
2025-05-29 13:22:28,651 - historical_data - INFO - 📈 Historical BUY signal at 12:31 @ 25075.28
2025-05-29 13:22:28,652 - logger - INFO - 📝 Signal #18 logged: SELL @ 25138.94
2025-05-29 13:22:28,652 - historical_data - INFO - 📈 Historical SELL signal at 12:57 @ 25138.94
2025-05-29 13:22:28,652 - logger - INFO - 📝 Signal #19 logged: BUY @ 25209.61
2025-05-29 13:22:28,652 - historical_data - INFO - 📈 Historical BUY signal at 13:00 @ 25209.61
2025-05-29 13:22:28,653 - historical_data - INFO - Recovery complete: Found 19 historical crossovers
2025-05-29 13:22:28,653 - __main__ - INFO - ✅ Recovered 19 historical crossovers
2025-05-29 13:22:28,654 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:22:28,654 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 13:22:28,654 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 13:22:28,655 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 13:22:28,655 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 13:22:28,655 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:22:28,655 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 13:22:29,030 - websocket - INFO - Websocket connected
2025-05-29 13:22:29,031 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 13:22:29,032 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:22:29,062 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 13:23:00,356 - logger - INFO - 📝 Signal #20 logged: SELL @ 24755.10
2025-05-29 13:23:00,357 - strategy - INFO - 🔔 SELL Signal: 1min 5_10 @ 24755.10 (EMA5: 25260.63, EMA10: 25351.89) P&L: 0.00
2025-05-29 14:00:44,834 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 14:00:44,834 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 14:00:44,834 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 14:00:45,090 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 14:00:45,122 - market_feed - INFO - Market feed stopped
2025-05-29 14:00:45,123 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 14:00:45,123 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 14:00:45,124 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 14:00:45,124 - __main__ - INFO - System stopped successfully
2025-05-29 14:01:52,650 - __main__ - INFO - Data directory: data
2025-05-29 14:01:52,651 - __main__ - INFO - EMA Trading System initialized
2025-05-29 14:01:52,651 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 14:01:52,665 - utils.market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 14:01:52,666 - data.historical_database - INFO - Historical Database initialized: data/historical
2025-05-29 14:01:52,666 - core.ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 14:01:52,666 - data.logger - INFO - Loaded daily state: 41 signals, P&L: 0.00
2025-05-29 14:01:52,666 - data.logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 14:01:52,666 - data.logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 14:01:52,666 - core.strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 14:01:52,666 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 14:01:52,667 - core.market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 14:01:52,667 - __main__ - INFO - All components initialized successfully
2025-05-29 14:01:52,667 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 1h 13m
2025-05-29 14:01:52,667 - __main__ - INFO - 📊 Checking historical database...
2025-05-29 14:01:52,673 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:01:52,679 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:01:52,680 - __main__ - INFO - 📊 Historical database current: 14 days, 5040 candles
2025-05-29 14:01:52,680 - __main__ - INFO - 🧮 Initializing EMAs with historical data...
2025-05-29 14:01:52,685 - data.historical_database - INFO - Loaded historical database with 14 days
2025-05-29 14:01:52,686 - data.historical_database - INFO - Retrieved 3600 historical prices from 10 days
2025-05-29 14:01:52,686 - core.ema - INFO - Loading EMA state from 3600 historical prices for 1min
2025-05-29 14:01:52,687 - core.ema - INFO - Reset data for timeframe: 1min
2025-05-29 14:01:52,690 - core.ema - INFO - EMA state loaded for 1min: EMA10=23961.84, EMA5=23945.28
2025-05-29 14:01:52,690 - __main__ - INFO - ✅ EMAs initialized with 3600 historical prices
2025-05-29 14:01:52,691 - __main__ - INFO - 📈 Current EMAs: EMA10=23961.84, EMA5=23945.28
2025-05-29 14:01:52,691 - __main__ - INFO - 🔄 New trading day detected - but EMAs already initialized with historical data
2025-05-29 14:01:52,691 - data.logger - INFO - Daily state reset for new trading session
2025-05-29 14:01:52,691 - utils.state_manager - INFO - Daily state reset
2025-05-29 14:01:52,691 - data.historical_data - INFO - Market open for 4.8 hours with no signals - recovery needed
2025-05-29 14:01:52,691 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 14:01:52,691 - data.historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:01:52.691546+05:30
2025-05-29 14:01:52,691 - data.historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:01:52.691546+05:30
2025-05-29 14:01:56,269 - data.historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 14:01:56,269 - data.historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 14:01:56,270 - data.historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 14:01:52.691546+05:30
2025-05-29 14:01:56,275 - data.historical_data - INFO - Generated 287 mock historical candles
2025-05-29 14:01:56,276 - data.logger - INFO - 📝 Signal #1 logged: BUY @ 24757.55
2025-05-29 14:01:56,276 - data.historical_data - INFO - 📈 Historical BUY signal at 09:15 @ 24757.55
2025-05-29 14:01:56,276 - data.logger - INFO - 📝 Signal #2 logged: SELL @ 24782.00
2025-05-29 14:01:56,277 - data.historical_data - INFO - 📈 Historical SELL signal at 09:40 @ 24782.00
2025-05-29 14:01:56,277 - data.logger - INFO - 📝 Signal #3 logged: BUY @ 24759.64
2025-05-29 14:01:56,277 - data.historical_data - INFO - 📈 Historical BUY signal at 10:01 @ 24759.64
2025-05-29 14:01:56,278 - data.logger - INFO - 📝 Signal #4 logged: SELL @ 24825.75
2025-05-29 14:01:56,278 - data.historical_data - INFO - 📈 Historical SELL signal at 10:16 @ 24825.75
2025-05-29 14:01:56,278 - data.logger - INFO - 📝 Signal #5 logged: BUY @ 24767.65
2025-05-29 14:01:56,279 - data.historical_data - INFO - 📈 Historical BUY signal at 10:32 @ 24767.65
2025-05-29 14:01:56,279 - data.logger - INFO - 📝 Signal #6 logged: SELL @ 24722.62
2025-05-29 14:01:56,279 - data.historical_data - INFO - 📈 Historical SELL signal at 10:33 @ 24722.62
2025-05-29 14:01:56,279 - data.logger - INFO - 📝 Signal #7 logged: BUY @ 24706.36
2025-05-29 14:01:56,280 - data.historical_data - INFO - 📈 Historical BUY signal at 10:43 @ 24706.36
2025-05-29 14:01:56,280 - data.logger - INFO - 📝 Signal #8 logged: SELL @ 24784.04
2025-05-29 14:01:56,280 - data.historical_data - INFO - 📈 Historical SELL signal at 11:00 @ 24784.04
2025-05-29 14:01:56,281 - data.logger - INFO - 📝 Signal #9 logged: BUY @ 24839.77
2025-05-29 14:01:56,281 - data.historical_data - INFO - 📈 Historical BUY signal at 11:03 @ 24839.77
2025-05-29 14:01:56,281 - data.logger - INFO - 📝 Signal #10 logged: SELL @ 24765.60
2025-05-29 14:01:56,281 - data.historical_data - INFO - 📈 Historical SELL signal at 11:05 @ 24765.60
2025-05-29 14:01:56,282 - data.logger - INFO - 📝 Signal #11 logged: BUY @ 24815.95
2025-05-29 14:01:56,282 - data.historical_data - INFO - 📈 Historical BUY signal at 11:11 @ 24815.95
2025-05-29 14:01:56,282 - data.logger - INFO - 📝 Signal #12 logged: SELL @ 24887.82
2025-05-29 14:01:56,283 - data.historical_data - INFO - 📈 Historical SELL signal at 11:31 @ 24887.82
2025-05-29 14:01:56,283 - data.logger - INFO - 📝 Signal #13 logged: BUY @ 24455.23
2025-05-29 14:01:56,283 - data.historical_data - INFO - 📈 Historical BUY signal at 12:22 @ 24455.23
2025-05-29 14:01:56,284 - data.logger - INFO - 📝 Signal #14 logged: SELL @ 24436.21
2025-05-29 14:01:56,284 - data.historical_data - INFO - 📈 Historical SELL signal at 12:28 @ 24436.21
2025-05-29 14:01:56,284 - data.logger - INFO - 📝 Signal #15 logged: BUY @ 24473.14
2025-05-29 14:01:56,284 - data.historical_data - INFO - 📈 Historical BUY signal at 12:29 @ 24473.14
2025-05-29 14:01:56,285 - data.logger - INFO - 📝 Signal #16 logged: SELL @ 24441.64
2025-05-29 14:01:56,285 - data.historical_data - INFO - 📈 Historical SELL signal at 12:32 @ 24441.64
2025-05-29 14:01:56,285 - data.logger - INFO - 📝 Signal #17 logged: BUY @ 24406.40
2025-05-29 14:01:56,286 - data.historical_data - INFO - 📈 Historical BUY signal at 12:47 @ 24406.40
2025-05-29 14:01:56,286 - data.logger - INFO - 📝 Signal #18 logged: SELL @ 24365.79
2025-05-29 14:01:56,286 - data.historical_data - INFO - 📈 Historical SELL signal at 12:53 @ 24365.79
2025-05-29 14:01:56,286 - data.logger - INFO - 📝 Signal #19 logged: BUY @ 24401.39
2025-05-29 14:01:56,287 - data.historical_data - INFO - 📈 Historical BUY signal at 12:54 @ 24401.39
2025-05-29 14:01:56,287 - data.logger - INFO - 📝 Signal #20 logged: SELL @ 24369.82
2025-05-29 14:01:56,287 - data.historical_data - INFO - 📈 Historical SELL signal at 12:55 @ 24369.82
2025-05-29 14:01:56,287 - data.logger - INFO - 📝 Signal #21 logged: BUY @ 24364.54
2025-05-29 14:01:56,288 - data.historical_data - INFO - 📈 Historical BUY signal at 13:08 @ 24364.54
2025-05-29 14:01:56,288 - data.logger - INFO - 📝 Signal #22 logged: SELL @ 24494.88
2025-05-29 14:01:56,288 - data.historical_data - INFO - 📈 Historical SELL signal at 13:32 @ 24494.88
2025-05-29 14:01:56,289 - data.logger - INFO - 📝 Signal #23 logged: BUY @ 24526.37
2025-05-29 14:01:56,289 - data.historical_data - INFO - 📈 Historical BUY signal at 13:36 @ 24526.37
2025-05-29 14:01:56,289 - data.logger - INFO - 📝 Signal #24 logged: SELL @ 24498.12
2025-05-29 14:01:56,289 - data.historical_data - INFO - 📈 Historical SELL signal at 13:37 @ 24498.12
2025-05-29 14:01:56,290 - data.logger - INFO - 📝 Signal #25 logged: BUY @ 24539.87
2025-05-29 14:01:56,290 - data.historical_data - INFO - 📈 Historical BUY signal at 13:38 @ 24539.87
2025-05-29 14:01:56,290 - data.historical_data - INFO - Recovery complete: Found 25 historical crossovers
2025-05-29 14:01:56,290 - __main__ - INFO - ✅ Recovered 25 historical crossovers
2025-05-29 14:01:56,291 - utils.state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 14:01:56,291 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 14:01:56,292 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 14:01:56,292 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 14:01:56,292 - core.market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 14:01:56,293 - core.market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 14:01:56,293 - core.market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 14:01:58,523 - websocket - INFO - Websocket connected
2025-05-29 14:01:58,524 - core.market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 14:01:58,524 - core.market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 14:01:58,980 - core.market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 14:07:00,627 - data.logger - INFO - 📝 Signal #26 logged: SELL @ 24731.95
2025-05-29 14:07:00,628 - core.strategy - INFO - 🔔 SELL Signal: 1min 5_10 @ 24731.95 (EMA5: 24737.97, EMA10: 24739.01) P&L: 0.00
2025-05-29 14:21:01,475 - data.logger - INFO - 📝 Signal #27 logged: BUY @ 24734.90
2025-05-29 14:21:01,476 - core.strategy - INFO - 🔔 BUY Signal: 1min 5_10 @ 24734.90 (EMA5: 24730.73, EMA10: 24729.95) P&L: -2.95
