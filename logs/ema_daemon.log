2025-05-29 12:27:55,994 - __main__ - INFO - Data directory: data
2025-05-29 12:27:55,995 - __main__ - INFO - EMA Trading System initialized
2025-05-29 12:27:55,995 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 12:27:56,005 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 12:27:56,005 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 12:27:56,005 - logger - INFO - Created new daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 12:27:56,005 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 12:27:56,005 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 12:27:56,006 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 12:27:56,006 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 12:27:56,006 - __main__ - INFO - All components initialized successfully
2025-05-29 12:27:56,006 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 47m
2025-05-29 12:27:56,006 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 12:27:56,006 - ema - INFO - Reset all EMA calculator data
2025-05-29 12:27:56,006 - logger - INFO - Daily state reset for new trading session
2025-05-29 12:27:56,006 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 12:27:56,006 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 12:27:56,006 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 12:27:56,006 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 12:27:56,006 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:27:56,006 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 12:27:56,310 - websocket - INFO - Websocket connected
2025-05-29 12:27:56,311 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 12:27:56,312 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:27:56,343 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 12:46:01,533 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 12:46:01,533 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 12:46:01,533 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 12:46:01,562 - market_feed - INFO - Market feed stopped
2025-05-29 12:46:01,562 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 12:46:01,563 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 12:46:01,563 - __main__ - INFO - System stopped successfully
2025-05-29 12:48:44,441 - __main__ - INFO - Data directory: data
2025-05-29 12:48:44,441 - __main__ - INFO - EMA Trading System initialized
2025-05-29 12:48:44,441 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 12:48:44,452 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 12:48:44,452 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 12:48:44,452 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 12:48:44,452 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 12:48:44,452 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 12:48:44,452 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 12:48:44,452 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 12:48:44,452 - __main__ - INFO - All components initialized successfully
2025-05-29 12:48:44,453 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 26m
2025-05-29 12:48:44,453 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 12:48:44,453 - ema - INFO - Reset all EMA calculator data
2025-05-29 12:48:44,453 - logger - INFO - Daily state reset for new trading session
2025-05-29 12:48:44,453 - state_manager - INFO - Daily state reset
2025-05-29 12:48:44,453 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 12:48:44,453 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 12:48:44,453 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 12:48:44,453 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 12:48:44,453 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:48:44,453 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 12:48:44,708 - websocket - INFO - Websocket connected
2025-05-29 12:48:44,709 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 12:48:44,709 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 12:48:44,738 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
2025-05-29 13:00:59,878 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-05-29 13:00:59,878 - __main__ - INFO - Stopping EMA Trading System...
2025-05-29 13:00:59,879 - market_feed - INFO - Stopping DhanHQ market feed...
2025-05-29 13:00:59,906 - market_feed - INFO - Market feed stopped
2025-05-29 13:00:59,906 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:00:59,906 - __main__ - INFO - 💾 EMA state saved for session continuity
2025-05-29 13:00:59,906 - logger - INFO - Closed daily CSV file for 20250529
2025-05-29 13:00:59,906 - __main__ - INFO - System stopped successfully
2025-05-29 13:01:09,857 - market_feed - WARNING - WebSocket closed: None - None
2025-05-29 13:03:15,568 - __main__ - INFO - Data directory: data
2025-05-29 13:03:15,568 - __main__ - INFO - EMA Trading System initialized
2025-05-29 13:03:15,568 - __main__ - INFO - Starting EMA Trading System...
2025-05-29 13:03:15,579 - market_hours - INFO - Market Hours: 09:15 - 15:15 IST
2025-05-29 13:03:15,579 - ema - INFO - EMA Calculator initialized with periods: [5, 10]
2025-05-29 13:03:15,580 - logger - INFO - Opened existing daily CSV file: data/nifty50_ema_signals_20250529.csv
2025-05-29 13:03:15,580 - logger - INFO - Signal Logger initialized with daily CSV file
2025-05-29 13:03:15,580 - strategy - INFO - EMA Strategy initialized for timeframes: ['1min']
2025-05-29 13:03:15,580 - __main__ - INFO - Using Live DhanHQ Market Feed
2025-05-29 13:03:15,580 - market_feed - INFO - DhanHQ Market Feed initialized for NIFTY 50
2025-05-29 13:03:15,580 - __main__ - INFO - All components initialized successfully
2025-05-29 13:03:15,580 - __main__ - INFO - Market Status: 🟢 MARKET OPEN - Closes in 2h 11m
2025-05-29 13:03:15,580 - __main__ - INFO - 🔄 New trading day detected - resetting EMA state
2025-05-29 13:03:15,580 - ema - INFO - Reset all EMA calculator data
2025-05-29 13:03:15,580 - logger - INFO - Daily state reset for new trading session
2025-05-29 13:03:15,580 - state_manager - INFO - Daily state reset
2025-05-29 13:03:15,581 - historical_data - INFO - Market open for 3.8 hours with no signals - recovery needed
2025-05-29 13:03:15,581 - __main__ - INFO - 🔍 Recovering historical EMA crossovers from market open...
2025-05-29 13:03:15,581 - historical_data - INFO - Recovering historical crossovers from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,581 - historical_data - INFO - Fetching historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,896 - historical_data - ERROR - HTTP error 500: {"errorCode":"INTERNAL_SERVER_ERROR","httpStatus":"INTERNAL_SERVER_ERROR","internalErrorCode":"RS-9005","internalErrorMessage":null}
2025-05-29 13:03:15,897 - historical_data - WARNING - API fetch failed, using mock data for demonstration
2025-05-29 13:03:15,897 - historical_data - INFO - Generating mock historical data from 2025-05-29 09:15:00+05:30 to 2025-05-29 13:03:15.581129+05:30
2025-05-29 13:03:15,898 - historical_data - INFO - Generated 229 mock historical candles
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #1 logged: SELL @ 24721.87
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 09:25 @ 24721.87
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #2 logged: BUY @ 24707.06
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical BUY signal at 09:38 @ 24707.06
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #3 logged: SELL @ 24787.47
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 10:04 @ 24787.47
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #4 logged: BUY @ 24833.09
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical BUY signal at 10:08 @ 24833.09
2025-05-29 13:03:15,898 - logger - INFO - 📝 Signal #5 logged: SELL @ 24790.24
2025-05-29 13:03:15,898 - historical_data - INFO - 📈 Historical SELL signal at 10:17 @ 24790.24
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #6 logged: BUY @ 24841.81
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 10:24 @ 24841.81
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #7 logged: SELL @ 25064.92
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 10:58 @ 25064.92
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #8 logged: BUY @ 25119.66
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:09 @ 25119.66
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #9 logged: SELL @ 25027.72
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 11:16 @ 25027.72
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #10 logged: BUY @ 25113.26
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:24 @ 25113.26
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #11 logged: SELL @ 25030.76
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical SELL signal at 11:26 @ 25030.76
2025-05-29 13:03:15,899 - logger - INFO - 📝 Signal #12 logged: BUY @ 25046.41
2025-05-29 13:03:15,899 - historical_data - INFO - 📈 Historical BUY signal at 11:35 @ 25046.41
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #13 logged: SELL @ 25258.03
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:01 @ 25258.03
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #14 logged: BUY @ 25301.63
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:02 @ 25301.63
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #15 logged: SELL @ 25269.30
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:10 @ 25269.30
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #16 logged: BUY @ 25310.95
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:15 @ 25310.95
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #17 logged: SELL @ 25335.76
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:27 @ 25335.76
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #18 logged: BUY @ 25343.37
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:37 @ 25343.37
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #19 logged: SELL @ 25370.05
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical SELL signal at 12:50 @ 25370.05
2025-05-29 13:03:15,900 - logger - INFO - 📝 Signal #20 logged: BUY @ 25408.15
2025-05-29 13:03:15,900 - historical_data - INFO - 📈 Historical BUY signal at 12:51 @ 25408.15
2025-05-29 13:03:15,901 - logger - INFO - 📝 Signal #21 logged: SELL @ 25395.60
2025-05-29 13:03:15,901 - historical_data - INFO - 📈 Historical SELL signal at 12:59 @ 25395.60
2025-05-29 13:03:15,901 - historical_data - INFO - Recovery complete: Found 21 historical crossovers
2025-05-29 13:03:15,901 - __main__ - INFO - ✅ Recovered 21 historical crossovers
2025-05-29 13:03:15,901 - state_manager - INFO - Saved daily state to data/ema_state_20250529.pkl
2025-05-29 13:03:15,901 - __main__ - INFO - 🚀 System started in BACKGROUND mode
2025-05-29 13:03:15,901 - __main__ - INFO - System will wait for market hours and run continuously
2025-05-29 13:03:15,901 - __main__ - INFO - 🔔 Market opened - starting data feed
2025-05-29 13:03:15,901 - market_feed - INFO - Starting DhanHQ market feed...
2025-05-29 13:03:15,901 - market_feed - INFO - Validated instrument: NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:03:15,901 - market_feed - INFO - Connecting to DhanHQ WebSocket...
2025-05-29 13:03:16,512 - websocket - INFO - Websocket connected
2025-05-29 13:03:16,513 - market_feed - INFO - ✅ WebSocket connected to DhanHQ
2025-05-29 13:03:16,514 - market_feed - INFO - Subscribed to NIFTY 50 (ID: 13, Exchange: IDX_I)
2025-05-29 13:03:16,545 - market_feed - INFO - Previous close: 24752.45, Previous OI: 0
