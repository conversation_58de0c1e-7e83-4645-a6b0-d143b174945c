# Installation & Setup Guide

## 🚀 Quick Start (5 Minutes)

### Step 1: Install Dependencies

```bash
# Using pip
pip install dhanhq websocket-client pandas numpy

# Or using uv (faster)
uv pip install dhanhq websocket-client pandas numpy
```

### Step 2: Configure DhanHQ Credentials

1. **Get your DhanHQ API credentials**:
   - Go to https://web.dhan.co
   - Login to your account
   - Navigate to: My Profile → Access DhanHQ APIs
   - Copy your Client ID and Access Token

2. **Update `config/config.json`**:
   ```json
   {
     "dhan_credentials": {
       "client_id": "YOUR_ACTUAL_CLIENT_ID_HERE",
       "access_token": "YOUR_ACTUAL_ACCESS_TOKEN_HERE"
     }
   }
   ```

### Step 3: Run the System

```bash
# Test first (uses mock data)
python simple_test.py

# Run live system
python src/main.py

# Or use the runner script
python run.py live
```

## 📋 Detailed Installation

### Prerequisites

- **Python 3.8+** (Check: `python --version`)
- **Internet connection** for live data
- **DhanHQ account** with API access enabled

### System Requirements

- **OS**: Windows, macOS, or Linux
- **RAM**: 512MB minimum (1GB recommended)
- **Storage**: 100MB for system + space for CSV logs
- **Network**: Stable internet for WebSocket connection

### Installation Methods

#### Method 1: Using pip (Standard)

```bash
# Install required packages
pip install dhanhq>=1.3.0
pip install websocket-client>=1.6.0
pip install pandas>=2.0.0
pip install numpy>=1.24.0

# Optional: For enhanced features
pip install python-dateutil>=2.8.0
```

#### Method 2: Using uv (Faster)

```bash
# Install uv first
pip install uv

# Install dependencies
uv pip install -r requirements.txt
```

#### Method 3: Using conda

```bash
# Create environment
conda create -n ema_trading python=3.9

# Activate environment
conda activate ema_trading

# Install packages
conda install pandas numpy
pip install dhanhq websocket-client
```

### Verification

```bash
# Test basic functionality
python simple_test.py

# Should output:
# ✅ Basic imports work
# ✅ Config file loaded
# ✅ EMA calculation logic works
# ✅ CSV writing works
```

## ⚙️ Configuration

### DhanHQ API Setup

1. **Enable API Access**:
   - Login to DhanHQ web platform
   - Go to Profile → API Settings
   - Enable API access if not already enabled
   - Note down your Client ID and Access Token

2. **API Permissions**:
   - Ensure you have market data permissions
   - Live feed access should be enabled
   - Check your subscription includes NIFTY 50 data

### Configuration Options

#### Basic Configuration (`config/config.json`):

```json
{
  "dhan_credentials": {
    "client_id": "1234567890",
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  },
  "instrument": {
    "name": "NIFTY 50",
    "security_id": "13",
    "exchange_segment": "IDX_I"
  },
  "ema_combinations": [
    {"short_ema": 5, "long_ema": 10},
    {"short_ema": 8, "long_ema": 21}
  ],
  "timeframes": ["1min", "5min"],
  "data_directory": "data",
  "logging": {
    "log_level": "INFO",
    "console_output": true
  },
  "trading": {
    "initial_capital": 100000,
    "position_size": 1
  }
}
```

#### Advanced Configuration:

```json
{
  "ema_combinations": [
    {"short_ema": 5, "long_ema": 10},
    {"short_ema": 8, "long_ema": 21},
    {"short_ema": 12, "long_ema": 26},
    {"short_ema": 9, "long_ema": 21},
    {"short_ema": 13, "long_ema": 34}
  ],
  "timeframes": ["1min", "5min", "10min", "15min"],
  "logging": {
    "log_level": "DEBUG",
    "console_output": true
  }
}
```

## 🏃‍♂️ Running the System

### Development Mode (Mock Data)

```bash
# Test without live connection
python run.py test

# Run with simulated data
python run.py mock
```

### Production Mode (Live Data)

```bash
# Run with live DhanHQ data
python run.py live

# Or directly
python src/main.py
```

### Background Mode (Linux/macOS)

```bash
# Run in background
nohup python src/main.py > ema_system.log 2>&1 &

# Check if running
ps aux | grep main.py

# Stop background process
pkill -f main.py
```

## 📊 Output & Monitoring

### File Structure After Running:

```
├── data/
│   ├── nifty50_ema_signals_1min_20250101_120000.csv
│   ├── nifty50_ema_signals_5min_20250101_120000.csv
│   └── nifty50_ema_signals_10min_20250101_120000.csv
├── logs/
│   └── ema_system_20250101.log
└── config/
    └── config.json
```

### Monitoring Commands:

```bash
# Watch live signals
tail -f logs/ema_system_$(date +%Y%m%d).log

# Check CSV output
head -5 data/nifty50_ema_signals_1min_*.csv

# Monitor system status
ps aux | grep python
```

## 🔧 Troubleshooting

### Common Issues & Solutions

#### 1. "Module not found" Error
```bash
# Solution: Install missing dependencies
pip install dhanhq websocket-client pandas numpy
```

#### 2. "Invalid credentials" Error
```bash
# Solution: Check your DhanHQ credentials
# 1. Verify Client ID and Access Token
# 2. Ensure API access is enabled
# 3. Check token expiry
```

#### 3. "WebSocket connection failed"
```bash
# Solution: Check network and DhanHQ status
# 1. Verify internet connection
# 2. Check DhanHQ API status
# 3. Try mock mode: python run.py mock
```

#### 4. "Permission denied" for CSV files
```bash
# Solution: Check file permissions
chmod 755 data/
mkdir -p logs data
```

#### 5. High CPU usage
```bash
# Solution: Reduce logging level
# In config.json: "log_level": "WARNING"
```

### Debug Mode

```bash
# Run with debug logging
# Edit config.json: "log_level": "DEBUG"
python src/main.py

# Check detailed logs
tail -f logs/ema_system_*.log | grep DEBUG
```

### Performance Monitoring

```bash
# Monitor memory usage
ps -o pid,ppid,cmd,%mem,%cpu -p $(pgrep -f main.py)

# Monitor file sizes
du -h data/ logs/

# Check WebSocket connection
netstat -an | grep :443
```

## 🔄 Daily Operations

### Market Hours Routine

#### Pre-Market (8:30 AM):
```bash
# Start system
python src/main.py

# Verify connection
tail -f logs/ema_system_$(date +%Y%m%d).log | grep "connected"
```

#### During Market (9:15 AM - 3:30 PM):
```bash
# Monitor signals
tail -f logs/ema_system_$(date +%Y%m%d).log | grep "Signal"

# Check CSV updates
ls -la data/nifty50_ema_signals_*_$(date +%Y%m%d)_*.csv
```

#### Post-Market (After 3:30 PM):
```bash
# Stop system (Ctrl+C or)
pkill -f main.py

# Analyze results
python -c "
import pandas as pd
df = pd.read_csv('data/nifty50_ema_signals_1min_*.csv')
print(f'Total signals: {len(df)}')
print(f'Total P&L: {df[\"Cumulative_PnL\"].iloc[-1]}')
"
```

## 🔒 Security & Best Practices

### API Security:
- Never commit credentials to version control
- Use environment variables for production
- Rotate access tokens regularly
- Monitor API usage limits

### System Security:
- Run with minimal privileges
- Keep dependencies updated
- Monitor log files for errors
- Backup configuration files

### Data Management:
- Archive old CSV files regularly
- Monitor disk space usage
- Backup important signal data
- Clean up old log files

## 📞 Support & Resources

### DhanHQ Resources:
- **API Documentation**: https://dhanhq.co/docs/
- **Support**: https://dhanhq.co/support/
- **Status Page**: https://status.dhan.co/

### System Support:
- Check `SYSTEM_EXPLANATION.md` for technical details
- Review log files for error messages
- Test with mock mode first
- Verify configuration settings

### Community:
- DhanHQ Discord/Telegram groups
- Python trading communities
- Algorithmic trading forums

---

**Ready to start? Run `python simple_test.py` to verify your setup!**
