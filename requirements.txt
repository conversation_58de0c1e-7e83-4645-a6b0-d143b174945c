# Real-time EMA Crossover Logging System for NIFTY 50
# Dependencies for DhanHQ WebSocket integration and data processing

# DhanHQ official Python library
dhanhq>=1.3.0

# WebSocket client for real-time data
websocket-client>=1.6.0

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0

# Logging and utilities
python-dateutil>=2.8.0
pytz>=2023.3

# Optional: For enhanced data visualization (if needed)
# matplotlib>=3.7.0
# plotly>=5.15.0

# Development and testing dependencies (optional)
# pytest>=7.4.0
# pytest-cov>=4.1.0
