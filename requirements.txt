# NIFTY 50 EMA Crossover Trading System
# Professional-grade algorithmic trading system dependencies

# Core dependencies
websocket-client>=1.6.0
requests>=2.31.0
pytz>=2023.3
python-dateutil>=2.8.0

# Optional: DhanHQ official library (if available)
# dhanhq>=1.3.0

# Optional: Data analysis (uncomment if needed)
# numpy>=1.24.0
# pandas>=2.0.0
# matplotlib>=3.7.0
# seaborn>=0.12.0

# Development dependencies (install separately with requirements-dev.txt)
# pytest>=7.4.0
# black>=23.0.0
# flake8>=6.0.0
# pre-commit>=3.0.0

# Optional: For enhanced data visualization (if needed)
# matplotlib>=3.7.0
# plotly>=5.15.0

# Development and testing dependencies (optional)
# pytest>=7.4.0
# pytest-cov>=4.1.0
