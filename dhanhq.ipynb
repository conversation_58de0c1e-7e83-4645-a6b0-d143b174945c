{"cells": [{"cell_type": "code", "execution_count": 5, "id": "2b1af6a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'errorType': 'HOLDING_ERROR', 'errorCode': 'DH-1111', 'errorMessage': 'No holdings available'}\n"]}, {"data": {"text/plain": ["{'errorType': 'HOLDING_ERROR',\n", " 'errorCode': 'DH-1111',\n", " 'errorMessage': 'No holdings available'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from dhanhq import dhanhq\n", "\n", "client_id = '1105577608'\n", "access_token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTUxOTc0LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.djsjGyzBJ7XsG_hjMsclUCadwwSUuN9USELllQ5HwxYna3OoR0CVTbDdc8o__OEVAx9WF6X3eMLmWRuc6UtVew'\n", "\n", "dhan = dhanhq(client_id, access_token)\n", "\n", "# print(dhan.get_holdings())\n", "\n", "def get_holdings():\n", "    data = dhan.get_holdings()\n", "    holdings = data['data']\n", "    print(holdings)\n", "    return holdings\n", "get_holdings()\n"]}], "metadata": {"kernelspec": {"display_name": "stock", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}