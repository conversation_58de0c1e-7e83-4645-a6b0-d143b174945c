#!/usr/bin/env python3
"""
Simple DhanHQ API Example using the official Python client
This demonstrates basic usage of DhanHQ APIs for market data and portfolio management.

Installation:
pip install dhanhq

Setup:
1. Login to https://web.dhan.co
2. Go to My Profile -> Access DhanHQ APIs
3. Copy your Client ID and Access Token
4. Replace the credentials below
"""

from dhanhq import dhanhq
import json
import time
from datetime import datetime, timedelta

# Replace with your actual credentials
CLIENT_ID = "1105577608"
ACCESS_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

def print_section(title, data):
    """Helper function to print data in a formatted way"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")
    if isinstance(data, (dict, list)):
        print(json.dumps(data, indent=2, default=str))
    else:
        print(data)
    print()

def main():
    """Main function demonstrating DhanHQ Python client usage"""

    # Initialize DhanHQ client
    dhan = dhanhq(CLIENT_ID, ACCESS_TOKEN)

    print("DhanHQ Python Client Examples")
    print("============================")

    try:
        # 1. Get User Profile
        print("\n1. Getting User Profile...")
        profile = dhan.get_fund_limits()  # Using fund limits as profile equivalent
        print_section("User Profile", profile)

        # 2. Get LTP (Last Traded Price) for instruments
        print("2. Getting LTP Data...")
        # Security IDs: 1333 (TCS), 11536 (RELIANCE) - these are examples
        ltp_data = dhan.ticker_data(
            securities={"NSE_EQ": [1333, 11536]}
        )
        print_section("LTP Data", ltp_data)

        # Wait to avoid rate limiting
        time.sleep(2)

        # 3. Get OHLC Data
        print("3. Getting OHLC Data...")
        ohlc_data = dhan.ohlc_data(
            securities={"NSE_EQ": [1333, 11536]}
        )
        print_section("OHLC Data", ohlc_data)

        # Wait to avoid rate limiting
        time.sleep(2)

        # 4. Get Market Quote (Full market depth)
        print("4. Getting Market Quote...")
        quote_data = dhan.quote_data(
            securities={"NSE_EQ": [1333]}
        )
        print_section("Market Quote", quote_data)

        # 5. Get Historical Data (Daily)
        print("5. Getting Historical Data...")
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

        historical_data = dhan.historical_daily_data(
            security_id="1333",
            exchange_segment=dhan.NSE,
            instrument_type="EQUITY",
            from_date=start_date,
            to_date=end_date
        )
        print_section("Historical Data (Last 30 Days)", historical_data)

        # 6. Get Intraday Data
        print("6. Getting Intraday Data...")
        end_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        start_datetime = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")

        intraday_data = dhan.intraday_minute_data(
            security_id="1333",
            exchange_segment=dhan.NSE,
            instrument_type="EQUITY",
            interval=5,  # 5-minute intervals
            from_date=start_datetime,
            to_date=end_datetime
        )
        print_section("Intraday Data (5-min intervals)", intraday_data)

        # 7. Get Holdings
        print("7. Getting Holdings...")
        holdings = dhan.get_holdings()
        print_section("Holdings", holdings)

        # 8. Get Positions
        print("8. Getting Positions...")
        positions = dhan.get_positions()
        print_section("Positions", positions)

        # 9. Get Funds
        print("9. Getting Fund Limits...")
        funds = dhan.get_fund_limits()
        print_section("Fund Limits", funds)

        # 10. Get Order List
        print("10. Getting Order List...")
        orders = dhan.get_order_list()
        print_section("Order List", orders)

        # 11. Get Trade Book
        print("11. Getting Trade Book...")
        trades = dhan.get_trade_book()
        print_section("Trade Book", trades)

    except Exception as e:
        print(f"Error occurred: {e}")
        print("Please check your credentials and network connection.")

def demo_order_placement():
    """
    Demo function showing how to place orders (commented out for safety)
    UNCOMMENT AND MODIFY CAREFULLY BEFORE USING IN LIVE TRADING
    """
    dhan = dhanhq(CLIENT_ID, ACCESS_TOKEN)

    # Example: Place a BUY order (COMMENTED OUT FOR SAFETY)
    """
    order_response = dhan.place_order(
        security_id='1333',  # TCS
        exchange_segment=dhan.NSE_EQ,
        transaction_type=dhan.BUY,
        quantity=1,
        order_type=dhan.LIMIT,
        product_type=dhan.INTRADAY,
        price=3500.0,
        validity=dhan.DAY,
        disclosed_quantity=0,
        after_market_order=False,
        amo_time='OPEN',
        bo_profit_value=0,
        bo_stop_loss_Value=0
    )
    print_section("Order Placement Response", order_response)
    """

    print("Order placement demo is commented out for safety.")
    print("Uncomment and modify the demo_order_placement() function to test order placement.")

def get_instrument_info():
    """Function to demonstrate how to work with instrument data"""
    print("\nInstrument Information:")
    print("======================")

    # Common Security IDs for reference (these may change, check instrument list)
    instruments = {
        "TCS": "1333",
        "RELIANCE": "11536",
        "INFY": "1594",
        "HDFC": "1330",
        "ICICIBANK": "4963"
    }

    print("Common NSE Equity Security IDs:")
    for symbol, security_id in instruments.items():
        print(f"{symbol}: {security_id}")

    print("\nExchange Segments:")
    print("NSE_EQ: NSE Equity")
    print("NSE_FNO: NSE Futures & Options")
    print("BSE_EQ: BSE Equity")
    print("BSE_FNO: BSE Futures & Options")
    print("NSE_CURRENCY: NSE Currency")
    print("MCX_COMM: MCX Commodity")

    print("\nTo get the complete instrument list:")
    print("Visit: https://dhanhq.co/docs/v2/instruments/")

if __name__ == "__main__":
    # Check if credentials are properly set
    if CLIENT_ID == "**********" or ACCESS_TOKEN == "your_access_token_here":
        print("⚠️  SETUP REQUIRED ⚠️")
        print("=" * 50)
        print("Please update your credentials:")
        print("1. Login to https://web.dhan.co")
        print("2. Go to My Profile -> Access DhanHQ APIs")
        print("3. Copy your Client ID and Access Token")
        print("4. Replace CLIENT_ID and ACCESS_TOKEN in this script")
        print("=" * 50)

        # Show instrument info even without credentials
        get_instrument_info()
    else:
        # Run the main examples
        main()

        # Show additional info
        get_instrument_info()

        # Show order demo info
        demo_order_placement()
