{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import Required Libraries\n", "Import the necessary libraries, including requests, pandas, and any other required libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'requests'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Import the necessary libraries\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mrequests\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'requests'"]}], "source": ["# Import the necessary libraries\n", "import requests\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fetch Live NIFTY Data\n", "Use an API or web scraping to fetch live data from the NIFTY."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["YF.download() has changed argument auto_adjust default to True\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-03-21 03:45:00+00:00</th>\n", "      <td>23156.599609</td>\n", "      <td>23168.250000</td>\n", "      <td>23134.300781</td>\n", "      <td>23168.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 03:46:00+00:00</th>\n", "      <td>23169.349609</td>\n", "      <td>23171.800781</td>\n", "      <td>23156.199219</td>\n", "      <td>23156.199219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 03:47:00+00:00</th>\n", "      <td>23173.650391</td>\n", "      <td>23181.099609</td>\n", "      <td>23169.250000</td>\n", "      <td>23169.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 03:48:00+00:00</th>\n", "      <td>23187.599609</td>\n", "      <td>23187.599609</td>\n", "      <td>23162.150391</td>\n", "      <td>23172.699219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 03:49:00+00:00</th>\n", "      <td>23194.750000</td>\n", "      <td>23197.750000</td>\n", "      <td>23185.699219</td>\n", "      <td>23187.800781</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 09:55:00+00:00</th>\n", "      <td>23358.250000</td>\n", "      <td>23363.099609</td>\n", "      <td>23358.150391</td>\n", "      <td>23360.050781</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 09:56:00+00:00</th>\n", "      <td>23358.849609</td>\n", "      <td>23359.449219</td>\n", "      <td>23355.849609</td>\n", "      <td>23359.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 09:57:00+00:00</th>\n", "      <td>23363.250000</td>\n", "      <td>23367.550781</td>\n", "      <td>23355.900391</td>\n", "      <td>23360.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 09:58:00+00:00</th>\n", "      <td>23358.949219</td>\n", "      <td>23373.550781</td>\n", "      <td>23358.949219</td>\n", "      <td>23362.449219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-21 09:59:00+00:00</th>\n", "      <td>23352.199219</td>\n", "      <td>23365.699219</td>\n", "      <td>23350.900391</td>\n", "      <td>23361.400391</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>375 rows × 5 columns</p>\n", "</div>"], "text/plain": ["Price                             Close          High           Low  \\\n", "Ticker                            ^NSEI         ^NSEI         ^NSEI   \n", "Datetime                                                              \n", "2025-03-21 03:45:00+00:00  23156.599609  23168.250000  23134.300781   \n", "2025-03-21 03:46:00+00:00  23169.349609  23171.800781  23156.199219   \n", "2025-03-21 03:47:00+00:00  23173.650391  23181.099609  23169.250000   \n", "2025-03-21 03:48:00+00:00  23187.599609  23187.599609  23162.150391   \n", "2025-03-21 03:49:00+00:00  23194.750000  23197.750000  23185.699219   \n", "...                                 ...           ...           ...   \n", "2025-03-21 09:55:00+00:00  23358.250000  23363.099609  23358.150391   \n", "2025-03-21 09:56:00+00:00  23358.849609  23359.449219  23355.849609   \n", "2025-03-21 09:57:00+00:00  23363.250000  23367.550781  23355.900391   \n", "2025-03-21 09:58:00+00:00  23358.949219  23373.550781  23358.949219   \n", "2025-03-21 09:59:00+00:00  23352.199219  23365.699219  23350.900391   \n", "\n", "Price                              Open Volume  \n", "Ticker                            ^NSEI  ^NSEI  \n", "Datetime                                        \n", "2025-03-21 03:45:00+00:00  23168.250000      0  \n", "2025-03-21 03:46:00+00:00  23156.199219      0  \n", "2025-03-21 03:47:00+00:00  23169.250000      0  \n", "2025-03-21 03:48:00+00:00  23172.699219      0  \n", "2025-03-21 03:49:00+00:00  23187.800781      0  \n", "...                                 ...    ...  \n", "2025-03-21 09:55:00+00:00  23360.050781      0  \n", "2025-03-21 09:56:00+00:00  23359.250000      0  \n", "2025-03-21 09:57:00+00:00  23360.000000      0  \n", "2025-03-21 09:58:00+00:00  23362.449219      0  \n", "2025-03-21 09:59:00+00:00  23361.400391      0  \n", "\n", "[375 rows x 5 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Datetime</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th></th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-03-21 03:45:00+00:00</td>\n", "      <td>23156.599609</td>\n", "      <td>23168.250000</td>\n", "      <td>23134.300781</td>\n", "      <td>23168.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-03-21 03:46:00+00:00</td>\n", "      <td>23169.349609</td>\n", "      <td>23171.800781</td>\n", "      <td>23156.199219</td>\n", "      <td>23156.199219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-03-21 03:47:00+00:00</td>\n", "      <td>23173.650391</td>\n", "      <td>23181.099609</td>\n", "      <td>23169.250000</td>\n", "      <td>23169.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-03-21 03:48:00+00:00</td>\n", "      <td>23187.599609</td>\n", "      <td>23187.599609</td>\n", "      <td>23162.150391</td>\n", "      <td>23172.699219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-03-21 03:49:00+00:00</td>\n", "      <td>23194.750000</td>\n", "      <td>23197.750000</td>\n", "      <td>23185.699219</td>\n", "      <td>23187.800781</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>2025-03-21 09:55:00+00:00</td>\n", "      <td>23358.250000</td>\n", "      <td>23363.099609</td>\n", "      <td>23358.150391</td>\n", "      <td>23360.050781</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>2025-03-21 09:56:00+00:00</td>\n", "      <td>23358.849609</td>\n", "      <td>23359.449219</td>\n", "      <td>23355.849609</td>\n", "      <td>23359.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td>2025-03-21 09:57:00+00:00</td>\n", "      <td>23363.250000</td>\n", "      <td>23367.550781</td>\n", "      <td>23355.900391</td>\n", "      <td>23360.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>373</th>\n", "      <td>2025-03-21 09:58:00+00:00</td>\n", "      <td>23358.949219</td>\n", "      <td>23373.550781</td>\n", "      <td>23358.949219</td>\n", "      <td>23362.449219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>374</th>\n", "      <td>2025-03-21 09:59:00+00:00</td>\n", "      <td>23352.199219</td>\n", "      <td>23365.699219</td>\n", "      <td>23350.900391</td>\n", "      <td>23361.400391</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>375 rows × 6 columns</p>\n", "</div>"], "text/plain": ["Price                   Datetime         Close          High           Low  \\\n", "Ticker                                   ^NSEI         ^NSEI         ^NSEI   \n", "0      2025-03-21 03:45:00+00:00  23156.599609  23168.250000  23134.300781   \n", "1      2025-03-21 03:46:00+00:00  23169.349609  23171.800781  23156.199219   \n", "2      2025-03-21 03:47:00+00:00  23173.650391  23181.099609  23169.250000   \n", "3      2025-03-21 03:48:00+00:00  23187.599609  23187.599609  23162.150391   \n", "4      2025-03-21 03:49:00+00:00  23194.750000  23197.750000  23185.699219   \n", "..                           ...           ...           ...           ...   \n", "370    2025-03-21 09:55:00+00:00  23358.250000  23363.099609  23358.150391   \n", "371    2025-03-21 09:56:00+00:00  23358.849609  23359.449219  23355.849609   \n", "372    2025-03-21 09:57:00+00:00  23363.250000  23367.550781  23355.900391   \n", "373    2025-03-21 09:58:00+00:00  23358.949219  23373.550781  23358.949219   \n", "374    2025-03-21 09:59:00+00:00  23352.199219  23365.699219  23350.900391   \n", "\n", "Price           Open Volume  \n", "Ticker         ^NSEI  ^NSEI  \n", "0       23168.250000      0  \n", "1       23156.199219      0  \n", "2       23169.250000      0  \n", "3       23172.699219      0  \n", "4       23187.800781      0  \n", "..               ...    ...  \n", "370     23360.050781      0  \n", "371     23359.250000      0  \n", "372     23360.000000      0  \n", "373     23362.449219      0  \n", "374     23361.400391      0  \n", "\n", "[375 rows x 6 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import yfinance as yf\n", "\n", "# Define the ticker symbol for NIFTY 50\n", "nifty_ticker = \"^NSEI\"\n", "\n", "# Fetch the data using yfinance\n", "nifty_data = yf.download(nifty_ticker, period=\"1d\", interval=\"1m\")\n", "\n", "# Display the fetched data\n", "display(nifty_data)\n", "\n", "# Convert the data to a pandas DataFrame\n", "df = nifty_data.reset_index()\n", "\n", "# Display the DataFrame\n", "display(df)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Process and Clean Data\n", "Process and clean the fetched data to make it suitable for analysis."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Datetime</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th></th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-03-21 03:45:00+00:00</td>\n", "      <td>23156.599609</td>\n", "      <td>23168.250000</td>\n", "      <td>23134.300781</td>\n", "      <td>23168.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-03-21 03:46:00+00:00</td>\n", "      <td>23169.349609</td>\n", "      <td>23171.800781</td>\n", "      <td>23156.199219</td>\n", "      <td>23156.199219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-03-21 03:47:00+00:00</td>\n", "      <td>23173.650391</td>\n", "      <td>23181.099609</td>\n", "      <td>23169.250000</td>\n", "      <td>23169.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-03-21 03:48:00+00:00</td>\n", "      <td>23187.599609</td>\n", "      <td>23187.599609</td>\n", "      <td>23162.150391</td>\n", "      <td>23172.699219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-03-21 03:49:00+00:00</td>\n", "      <td>23194.750000</td>\n", "      <td>23197.750000</td>\n", "      <td>23185.699219</td>\n", "      <td>23187.800781</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>2025-03-21 09:55:00+00:00</td>\n", "      <td>23358.250000</td>\n", "      <td>23363.099609</td>\n", "      <td>23358.150391</td>\n", "      <td>23360.050781</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>2025-03-21 09:56:00+00:00</td>\n", "      <td>23358.849609</td>\n", "      <td>23359.449219</td>\n", "      <td>23355.849609</td>\n", "      <td>23359.250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td>2025-03-21 09:57:00+00:00</td>\n", "      <td>23363.250000</td>\n", "      <td>23367.550781</td>\n", "      <td>23355.900391</td>\n", "      <td>23360.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>373</th>\n", "      <td>2025-03-21 09:58:00+00:00</td>\n", "      <td>23358.949219</td>\n", "      <td>23373.550781</td>\n", "      <td>23358.949219</td>\n", "      <td>23362.449219</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>374</th>\n", "      <td>2025-03-21 09:59:00+00:00</td>\n", "      <td>23352.199219</td>\n", "      <td>23365.699219</td>\n", "      <td>23350.900391</td>\n", "      <td>23361.400391</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>375 rows × 6 columns</p>\n", "</div>"], "text/plain": ["Price                   Datetime         Close          High           Low  \\\n", "Ticker                                   ^NSEI         ^NSEI         ^NSEI   \n", "0      2025-03-21 03:45:00+00:00  23156.599609  23168.250000  23134.300781   \n", "1      2025-03-21 03:46:00+00:00  23169.349609  23171.800781  23156.199219   \n", "2      2025-03-21 03:47:00+00:00  23173.650391  23181.099609  23169.250000   \n", "3      2025-03-21 03:48:00+00:00  23187.599609  23187.599609  23162.150391   \n", "4      2025-03-21 03:49:00+00:00  23194.750000  23197.750000  23185.699219   \n", "..                           ...           ...           ...           ...   \n", "370    2025-03-21 09:55:00+00:00  23358.250000  23363.099609  23358.150391   \n", "371    2025-03-21 09:56:00+00:00  23358.849609  23359.449219  23355.849609   \n", "372    2025-03-21 09:57:00+00:00  23363.250000  23367.550781  23355.900391   \n", "373    2025-03-21 09:58:00+00:00  23358.949219  23373.550781  23358.949219   \n", "374    2025-03-21 09:59:00+00:00  23352.199219  23365.699219  23350.900391   \n", "\n", "Price           Open Volume  \n", "Ticker         ^NSEI  ^NSEI  \n", "0       23168.250000      0  \n", "1       23156.199219      0  \n", "2       23169.250000      0  \n", "3       23172.699219      0  \n", "4       23187.800781      0  \n", "..               ...    ...  \n", "370     23360.050781      0  \n", "371     23359.250000      0  \n", "372     23360.000000      0  \n", "373     23362.449219      0  \n", "374     23361.400391      0  \n", "\n", "[375 rows x 6 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Process and Clean Data\n", "\n", "# Convert columns to appropriate data types\n", "df[('Close', '^NSEI')] = pd.to_numeric(df[('Close', '^NSEI')], errors='coerce')\n", "df[('High', '^NSEI')] = pd.to_numeric(df[('High', '^NSEI')], errors='coerce')\n", "df[('Low', '^NSEI')] = pd.to_numeric(df[('Low', '^NSEI')], errors='coerce')\n", "df[('Open', '^NSEI')] = pd.to_numeric(df[('Open', '^NSEI')], errors='coerce')\n", "df[('Volume', '^NSEI')] = pd.to_numeric(df[('Volume', '^NSEI')], errors='coerce')\n", "\n", "# Handle missing values\n", "df.dropna(inplace=True)\n", "\n", "# Reset the index after dropping rows\n", "df.reset_index(drop=True, inplace=True)\n", "\n", "# Display the cleaned DataFrame\n", "display(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Analyze Stock Data\n", "Analyze the stock data using various financial metrics and indicators."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Datetime</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "      <th>daily_return</th>\n", "      <th>50_day_MA</th>\n", "      <th>200_day_MA</th>\n", "      <th>RSI</th>\n", "      <th>20_day_MA</th>\n", "      <th>20_day_std</th>\n", "      <th>upper_band</th>\n", "      <th>lower_band</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th></th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-03-21 03:45:00+00:00</td>\n", "      <td>23156.599609</td>\n", "      <td>23168.250000</td>\n", "      <td>23134.300781</td>\n", "      <td>23168.250000</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-03-21 03:46:00+00:00</td>\n", "      <td>23169.349609</td>\n", "      <td>23171.800781</td>\n", "      <td>23156.199219</td>\n", "      <td>23156.199219</td>\n", "      <td>0</td>\n", "      <td>0.000551</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-03-21 03:47:00+00:00</td>\n", "      <td>23173.650391</td>\n", "      <td>23181.099609</td>\n", "      <td>23169.250000</td>\n", "      <td>23169.250000</td>\n", "      <td>0</td>\n", "      <td>0.000186</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-03-21 03:48:00+00:00</td>\n", "      <td>23187.599609</td>\n", "      <td>23187.599609</td>\n", "      <td>23162.150391</td>\n", "      <td>23172.699219</td>\n", "      <td>0</td>\n", "      <td>0.000602</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-03-21 03:49:00+00:00</td>\n", "      <td>23194.750000</td>\n", "      <td>23197.750000</td>\n", "      <td>23185.699219</td>\n", "      <td>23187.800781</td>\n", "      <td>0</td>\n", "      <td>0.000308</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>2025-03-21 09:55:00+00:00</td>\n", "      <td>23358.250000</td>\n", "      <td>23363.099609</td>\n", "      <td>23358.150391</td>\n", "      <td>23360.050781</td>\n", "      <td>0</td>\n", "      <td>-0.000032</td>\n", "      <td>23349.542930</td>\n", "      <td>23358.044238</td>\n", "      <td>50.058845</td>\n", "      <td>23352.544824</td>\n", "      <td>7.075662</td>\n", "      <td>23366.696148</td>\n", "      <td>23338.393501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>2025-03-21 09:56:00+00:00</td>\n", "      <td>23358.849609</td>\n", "      <td>23359.449219</td>\n", "      <td>23355.849609</td>\n", "      <td>23359.250000</td>\n", "      <td>0</td>\n", "      <td>0.000026</td>\n", "      <td>23349.586914</td>\n", "      <td>23358.042236</td>\n", "      <td>51.034978</td>\n", "      <td>23353.274805</td>\n", "      <td>6.926393</td>\n", "      <td>23367.127592</td>\n", "      <td>23339.422018</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td>2025-03-21 09:57:00+00:00</td>\n", "      <td>23363.250000</td>\n", "      <td>23367.550781</td>\n", "      <td>23355.900391</td>\n", "      <td>23360.000000</td>\n", "      <td>0</td>\n", "      <td>0.000188</td>\n", "      <td>23349.670898</td>\n", "      <td>23358.078740</td>\n", "      <td>53.595745</td>\n", "      <td>23354.407324</td>\n", "      <td>6.588329</td>\n", "      <td>23367.583982</td>\n", "      <td>23341.230666</td>\n", "    </tr>\n", "    <tr>\n", "      <th>373</th>\n", "      <td>2025-03-21 09:58:00+00:00</td>\n", "      <td>23358.949219</td>\n", "      <td>23373.550781</td>\n", "      <td>23358.949219</td>\n", "      <td>23362.449219</td>\n", "      <td>0</td>\n", "      <td>-0.000184</td>\n", "      <td>23349.590898</td>\n", "      <td>23358.059736</td>\n", "      <td>63.158159</td>\n", "      <td>23354.969824</td>\n", "      <td>6.464549</td>\n", "      <td>23367.898923</td>\n", "      <td>23342.040726</td>\n", "    </tr>\n", "    <tr>\n", "      <th>374</th>\n", "      <td>2025-03-21 09:59:00+00:00</td>\n", "      <td>23352.199219</td>\n", "      <td>23365.699219</td>\n", "      <td>23350.900391</td>\n", "      <td>23361.400391</td>\n", "      <td>0</td>\n", "      <td>-0.000289</td>\n", "      <td>23349.372891</td>\n", "      <td>23358.019482</td>\n", "      <td>55.717762</td>\n", "      <td>23354.672266</td>\n", "      <td>6.447386</td>\n", "      <td>23367.567038</td>\n", "      <td>23341.777493</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>375 rows × 14 columns</p>\n", "</div>"], "text/plain": ["Price                   Datetime         Close          High           Low  \\\n", "Ticker                                   ^NSEI         ^NSEI         ^NSEI   \n", "0      2025-03-21 03:45:00+00:00  23156.599609  23168.250000  23134.300781   \n", "1      2025-03-21 03:46:00+00:00  23169.349609  23171.800781  23156.199219   \n", "2      2025-03-21 03:47:00+00:00  23173.650391  23181.099609  23169.250000   \n", "3      2025-03-21 03:48:00+00:00  23187.599609  23187.599609  23162.150391   \n", "4      2025-03-21 03:49:00+00:00  23194.750000  23197.750000  23185.699219   \n", "..                           ...           ...           ...           ...   \n", "370    2025-03-21 09:55:00+00:00  23358.250000  23363.099609  23358.150391   \n", "371    2025-03-21 09:56:00+00:00  23358.849609  23359.449219  23355.849609   \n", "372    2025-03-21 09:57:00+00:00  23363.250000  23367.550781  23355.900391   \n", "373    2025-03-21 09:58:00+00:00  23358.949219  23373.550781  23358.949219   \n", "374    2025-03-21 09:59:00+00:00  23352.199219  23365.699219  23350.900391   \n", "\n", "Price           Open Volume daily_return     50_day_MA    200_day_MA  \\\n", "Ticker         ^NSEI  ^NSEI                                            \n", "0       23168.250000      0          NaN           NaN           NaN   \n", "1       23156.199219      0     0.000551           NaN           NaN   \n", "2       23169.250000      0     0.000186           NaN           NaN   \n", "3       23172.699219      0     0.000602           NaN           NaN   \n", "4       23187.800781      0     0.000308           NaN           NaN   \n", "..               ...    ...          ...           ...           ...   \n", "370     23360.050781      0    -0.000032  23349.542930  23358.044238   \n", "371     23359.250000      0     0.000026  23349.586914  23358.042236   \n", "372     23360.000000      0     0.000188  23349.670898  23358.078740   \n", "373     23362.449219      0    -0.000184  23349.590898  23358.059736   \n", "374     23361.400391      0    -0.000289  23349.372891  23358.019482   \n", "\n", "Price         RSI     20_day_MA 20_day_std    upper_band    lower_band  \n", "Ticker                                                                  \n", "0             NaN           NaN        NaN           NaN           NaN  \n", "1             NaN           NaN        NaN           NaN           NaN  \n", "2             NaN           NaN        NaN           NaN           NaN  \n", "3             NaN           NaN        NaN           NaN           NaN  \n", "4             NaN           NaN        NaN           NaN           NaN  \n", "..            ...           ...        ...           ...           ...  \n", "370     50.058845  23352.544824   7.075662  23366.696148  23338.393501  \n", "371     51.034978  23353.274805   6.926393  23367.127592  23339.422018  \n", "372     53.595745  23354.407324   6.588329  23367.583982  23341.230666  \n", "373     63.158159  23354.969824   6.464549  23367.898923  23342.040726  \n", "374     55.717762  23354.672266   6.447386  23367.567038  23341.777493  \n", "\n", "[375 rows x 14 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Datetime</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "      <th>daily_return</th>\n", "      <th>50_day_MA</th>\n", "      <th>200_day_MA</th>\n", "      <th>RSI</th>\n", "      <th>20_day_MA</th>\n", "      <th>20_day_std</th>\n", "      <th>upper_band</th>\n", "      <th>lower_band</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th></th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th>^NSEI</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>236</th>\n", "      <td>2025-03-21 07:41:00+00:00</td>\n", "      <td>23385.250000</td>\n", "      <td>23391.400391</td>\n", "      <td>23382.250000</td>\n", "      <td>23390.150391</td>\n", "      <td>0</td>\n", "      <td>-0.000250</td>\n", "      <td>23384.773984</td>\n", "      <td>23337.355986</td>\n", "      <td>28.279314</td>\n", "      <td>23393.580078</td>\n", "      <td>4.477038</td>\n", "      <td>23402.534154</td>\n", "      <td>23384.626003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>239</th>\n", "      <td>2025-03-21 07:44:00+00:00</td>\n", "      <td>23371.400391</td>\n", "      <td>23392.099609</td>\n", "      <td>23366.949219</td>\n", "      <td>23390.900391</td>\n", "      <td>0</td>\n", "      <td>-0.000849</td>\n", "      <td>23384.858008</td>\n", "      <td>23339.283984</td>\n", "      <td>25.488291</td>\n", "      <td>23392.000098</td>\n", "      <td>6.648062</td>\n", "      <td>23405.296222</td>\n", "      <td>23378.703973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>2025-03-21 07:45:00+00:00</td>\n", "      <td>23356.000000</td>\n", "      <td>23373.150391</td>\n", "      <td>23355.750000</td>\n", "      <td>23371.150391</td>\n", "      <td>0</td>\n", "      <td>-0.000659</td>\n", "      <td>23384.391016</td>\n", "      <td>23339.761230</td>\n", "      <td>20.282133</td>\n", "      <td>23389.987598</td>\n", "      <td>10.353439</td>\n", "      <td>23410.694475</td>\n", "      <td>23369.280720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>241</th>\n", "      <td>2025-03-21 07:46:00+00:00</td>\n", "      <td>23357.099609</td>\n", "      <td>23362.250000</td>\n", "      <td>23351.699219</td>\n", "      <td>23357.199219</td>\n", "      <td>0</td>\n", "      <td>0.000047</td>\n", "      <td>23383.786992</td>\n", "      <td>23340.295479</td>\n", "      <td>22.309970</td>\n", "      <td>23388.035059</td>\n", "      <td>12.574142</td>\n", "      <td>23413.183342</td>\n", "      <td>23362.886775</td>\n", "    </tr>\n", "    <tr>\n", "      <th>242</th>\n", "      <td>2025-03-21 07:47:00+00:00</td>\n", "      <td>23355.750000</td>\n", "      <td>23359.800781</td>\n", "      <td>23349.949219</td>\n", "      <td>23356.699219</td>\n", "      <td>0</td>\n", "      <td>-0.000058</td>\n", "      <td>23383.190000</td>\n", "      <td>23340.754727</td>\n", "      <td>22.703137</td>\n", "      <td>23385.865039</td>\n", "      <td>14.195378</td>\n", "      <td>23414.255795</td>\n", "      <td>23357.474283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>2025-03-21 07:48:00+00:00</td>\n", "      <td>23359.599609</td>\n", "      <td>23363.099609</td>\n", "      <td>23352.750000</td>\n", "      <td>23355.550781</td>\n", "      <td>0</td>\n", "      <td>0.000165</td>\n", "      <td>23382.825977</td>\n", "      <td>23341.231729</td>\n", "      <td>29.419996</td>\n", "      <td>23383.887500</td>\n", "      <td>14.980383</td>\n", "      <td>23413.848265</td>\n", "      <td>23353.926735</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Price                   Datetime         Close          High           Low  \\\n", "Ticker                                   ^NSEI         ^NSEI         ^NSEI   \n", "236    2025-03-21 07:41:00+00:00  23385.250000  23391.400391  23382.250000   \n", "239    2025-03-21 07:44:00+00:00  23371.400391  23392.099609  23366.949219   \n", "240    2025-03-21 07:45:00+00:00  23356.000000  23373.150391  23355.750000   \n", "241    2025-03-21 07:46:00+00:00  23357.099609  23362.250000  23351.699219   \n", "242    2025-03-21 07:47:00+00:00  23355.750000  23359.800781  23349.949219   \n", "243    2025-03-21 07:48:00+00:00  23359.599609  23363.099609  23352.750000   \n", "\n", "Price           Open Volume daily_return     50_day_MA    200_day_MA  \\\n", "Ticker         ^NSEI  ^NSEI                                            \n", "236     23390.150391      0    -0.000250  23384.773984  23337.355986   \n", "239     23390.900391      0    -0.000849  23384.858008  23339.283984   \n", "240     23371.150391      0    -0.000659  23384.391016  23339.761230   \n", "241     23357.199219      0     0.000047  23383.786992  23340.295479   \n", "242     23356.699219      0    -0.000058  23383.190000  23340.754727   \n", "243     23355.550781      0     0.000165  23382.825977  23341.231729   \n", "\n", "Price         RSI     20_day_MA 20_day_std    upper_band    lower_band  \n", "Ticker                                                                  \n", "236     28.279314  23393.580078   4.477038  23402.534154  23384.626003  \n", "239     25.488291  23392.000098   6.648062  23405.296222  23378.703973  \n", "240     20.282133  23389.987598  10.353439  23410.694475  23369.280720  \n", "241     22.309970  23388.035059  12.574142  23413.183342  23362.886775  \n", "242     22.703137  23385.865039  14.195378  23414.255795  23357.474283  \n", "243     29.419996  23383.887500  14.980383  23413.848265  23353.926735  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Analyze Stock Data\n", "\n", "# Calculate the daily return\n", "df['daily_return'] = df[('Close', '^NSEI')].pct_change()\n", "\n", "# Calculate the moving averages\n", "df['50_day_MA'] = df[('Close', '^NSEI')].rolling(window=50).mean()\n", "df['200_day_MA'] = df[('Close', '^NSEI')].rolling(window=200).mean()\n", "\n", "# Calculate the Relative Strength Index (RSI)\n", "def calculate_rsi(data, window):\n", "    delta = data.diff()\n", "    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()\n", "    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()\n", "    rs = gain / loss\n", "    rsi = 100 - (100 / (1 + rs))\n", "    return rsi\n", "\n", "df['RSI'] = calculate_rsi(df[('Close', '^NSEI')], window=14)\n", "\n", "# Calculate the Bollinger Bands\n", "df['20_day_MA'] = df[('Close', '^NSEI')].rolling(window=20).mean()\n", "df['20_day_std'] = df[('Close', '^NSEI')].rolling(window=20).std()\n", "df['upper_band'] = df['20_day_MA'] + (df['20_day_std'] * 2)\n", "df['lower_band'] = df['20_day_MA'] - (df['20_day_std'] * 2)\n", "\n", "# Display the DataFrame with the new metrics\n", "display(df)\n", "\n", "# Filter stocks based on investment criteria\n", "# Example criteria: RSI < 30 (oversold), last price above 200-day MA\n", "investment_criteria = (df['RSI'] < 30) & (df[('Close', '^NSEI')] > df['200_day_MA'])\n", "investment_stocks = df[investment_criteria]\n", "\n", "# Display the stocks that meet the investment criteria\n", "display(investment_stocks)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Identify Investment Opportunities\n", "Identify potential investment opportunities based on the analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify Investment Opportunities\n", "\n", "# Filter stocks based on additional investment criteria\n", "# Example criteria: last price within the Bollinger Bands, positive daily return\n", "additional_criteria = (df['lastPrice'] > df['lower_band']) & (df['lastPrice'] < df['upper_band']) & (df['daily_return'] > 0)\n", "\n", "# Combine the criteria\n", "final_criteria = investment_criteria & additional_criteria\n", "\n", "# Get the final list of stocks to invest in\n", "final_investment_stocks = df[final_criteria]\n", "\n", "# Display the final list of stocks that meet all investment criteria\n", "display(final_investment_stocks)"]}], "metadata": {"kernelspec": {"display_name": "stock", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}