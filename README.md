# Real-time EMA Crossover Logging System for NIFTY 50

A complete real-time trading system that connects to DhanHQ's WebSocket API to stream live NIFTY 50 data, calculates Exponential Moving Averages (EMAs), detects crossover signals, and logs all trading signals to CSV files with P&L tracking.

## 🚀 Features

- **Real-time WebSocket Data**: Live NIFTY 50 tick data from DhanHQ
- **Multi-timeframe Analysis**: 1min, 5min, 10min candles from tick data
- **Multiple EMA Combinations**: Configurable EMA pairs (5/10, 8/21, 12/26)
- **Crossover Detection**: Golden Cross (BUY) and Death Cross (SELL) signals
- **CSV Logging**: Separate files per timeframe with comprehensive data
- **P&L Tracking**: Real-time profit/loss calculations
- **Error Handling**: Graceful WebSocket reconnection and error recovery
- **Mock Mode**: Testing without live market connection

## 📁 Project Structure

```
├── src/
│   ├── main.py          # Main entry point
│   ├── ema.py           # EMA calculation engine
│   ├── strategy.py      # Crossover strategy logic
│   ├── logger.py        # CSV logging with P&L tracking
│   └── market_feed.py   # DhanHQ WebSocket client
├── config/
│   └── config.json      # Configuration settings
├── data/                # Output CSV files
├── logs/                # System logs
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## 🛠️ Installation

### Prerequisites

1. **Python 3.8+** installed
2. **DhanHQ Account** with API access
3. **Internet connection** for live data

### Setup Instructions

1. **Clone or download** this project to your local machine

2. **Install dependencies** using uv (recommended) or pip:

   ```bash
   # Using uv (faster)
   uv pip install -r requirements.txt
   
   # Or using regular pip
   pip install -r requirements.txt
   ```

3. **Configure DhanHQ credentials** in `config/config.json`:
   
   ```json
   {
     "dhan_credentials": {
       "client_id": "YOUR_ACTUAL_CLIENT_ID",
       "access_token": "YOUR_ACTUAL_ACCESS_TOKEN"
     }
   }
   ```

   **Get your credentials from**: https://web.dhan.co → My Profile → Access DhanHQ APIs

## ⚙️ Configuration

Edit `config/config.json` to customize:

```json
{
  "dhan_credentials": {
    "client_id": "YOUR_CLIENT_ID_HERE",
    "access_token": "YOUR_ACCESS_TOKEN_HERE"
  },
  "instrument": {
    "name": "NIFTY 50",
    "security_id": "13",
    "exchange_segment": "IDX_I"
  },
  "ema_combinations": [
    {"short_ema": 5, "long_ema": 10},
    {"short_ema": 8, "long_ema": 21},
    {"short_ema": 12, "long_ema": 26}
  ],
  "timeframes": ["1min", "5min", "10min"],
  "data_directory": "data",
  "logging": {
    "log_level": "INFO",
    "console_output": true
  },
  "trading": {
    "initial_capital": 100000,
    "position_size": 1
  }
}
```

## 🚀 Running the System

### Live Trading Mode

```bash
# Using uv
uv run src/main.py

# Or using Python directly
python src/main.py
```

### Testing Mode (Mock Data)

For testing without live market connection, modify `src/main.py` to use `MockMarketFeed` instead of `DhanMarketFeed`.

## 📊 Output Files

The system generates CSV files in the `data/` directory:

### CSV Format
```
Datetime,Action,Price,EMA_Combo,Entry_Exit,PnL,Short_EMA_Value,Long_EMA_Value,Candle_Open,Candle_High,Candle_Low,Candle_Close,Candle_Volume,Cumulative_PnL
```

### Example Output
```
2025-01-01 09:15:00,BUY,19500.50,5/10,ENTRY,0.00,19498.25,19495.80,19499.00,19501.25,19498.75,19500.50,1250,0.00
2025-01-01 09:18:00,SELL,19485.75,5/10,ENTRY,-14.75,19490.60,19493.45,19488.25,19490.00,19484.50,19485.75,980,-14.75
```

## 📈 Trading Logic

### EMA Crossover Signals

1. **Golden Cross (BUY Signal)**: Short EMA crosses above Long EMA
2. **Death Cross (SELL Signal)**: Short EMA crosses below Long EMA

### Signal Processing

- Real-time tick data → OHLC candles → EMA calculation → Crossover detection → CSV logging
- Separate tracking for each timeframe and EMA combination
- P&L calculation based on entry/exit prices

## 🔧 Troubleshooting

### Common Issues

1. **"Invalid credentials"**: Update your DhanHQ client_id and access_token
2. **"WebSocket connection failed"**: Check internet connection and DhanHQ API status
3. **"Module not found"**: Install dependencies with `pip install -r requirements.txt`

### Logs

Check `logs/ema_system_YYYYMMDD.log` for detailed system logs.

## 📋 Market Hours

- **Indian Market Hours**: 9:15 AM - 3:30 PM IST (Monday-Friday)
- **Pre-market**: 9:00 AM - 9:15 AM IST
- **After-market**: 3:40 PM - 4:00 PM IST

## ⚠️ Important Notes

1. **Paper Trading**: This system is for educational/analysis purposes
2. **Risk Management**: Always implement proper risk management
3. **Market Data**: Requires active DhanHQ subscription for live data
4. **Testing**: Test thoroughly before any live trading
5. **Compliance**: Ensure compliance with local trading regulations

## 🔄 System Workflow

1. **Startup**: Load configuration and validate credentials
2. **Connection**: Connect to DhanHQ WebSocket and subscribe to NIFTY 50
3. **Data Processing**: Convert ticks → candles → EMAs → signals
4. **Logging**: Write all signals to CSV with P&L tracking
5. **Monitoring**: Continuous operation with error handling

## 📞 Support

For DhanHQ API support: https://dhanhq.co/docs/

---

**Disclaimer**: This software is for educational purposes only. Trading involves risk of financial loss. Always consult with financial advisors before making investment decisions.
