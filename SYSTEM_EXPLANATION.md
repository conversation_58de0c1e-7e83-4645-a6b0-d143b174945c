# EMA Crossover System - Complete Technical Explanation

## 🎯 System Overview

This is a professional-grade real-time EMA crossover trading system designed for Indian markets, specifically NIFTY 50. It connects to DhanHQ's live WebSocket feed, processes tick data, generates synthetic OHLC candles, calculates EMAs, detects crossover signals, and logs everything to CSV files with P&L tracking.

## 🏗️ Architecture

### Core Components

1. **Main Controller (`src/main.py`)**
   - System orchestrator and entry point
   - Configuration management
   - Component initialization and lifecycle management
   - Graceful shutdown handling

2. **EMA Calculator (`src/ema.py`)**
   - Efficient exponential moving average calculations
   - Multi-period and multi-timeframe support
   - Real-time crossover signal detection
   - Historical data management with memory optimization

3. **Trading Strategy (`src/strategy.py`)**
   - Tick-to-candle conversion for multiple timeframes
   - OHLC candle generation from real-time ticks
   - EMA crossover logic implementation
   - Position tracking and P&L calculation

4. **Signal Logger (`src/logger.py`)**
   - Thread-safe CSV logging
   - Separate files per timeframe
   - Comprehensive signal data capture
   - Real-time P&L tracking and statistics

5. **Market Feed (`src/market_feed.py`)**
   - DhanHQ WebSocket client implementation
   - Automatic reconnection on disconnects
   - Mock feed for testing without live connection
   - Real-time tick data processing

## 📊 Data Flow

```
DhanHQ WebSocket → Tick Data → OHLC Candles → EMA Calculation → Crossover Detection → CSV Logging
```

### Detailed Flow:

1. **Market Data Ingestion**
   - WebSocket connects to DhanHQ live feed
   - Subscribes to NIFTY 50 (Security ID: 13, Exchange: IDX_I)
   - Receives real-time tick data with price, volume, timestamp

2. **Candle Generation**
   - Groups ticks into time-based buckets (1min, 5min, 10min)
   - Calculates OHLC (Open, High, Low, Close) for each timeframe
   - Maintains separate candle streams for each timeframe

3. **EMA Calculation**
   - Uses exponential smoothing: EMA = (Price × Multiplier) + (Previous EMA × (1 - Multiplier))
   - Multiplier = 2 / (Period + 1)
   - Calculates multiple EMAs simultaneously (5, 8, 10, 12, 21, 26)

4. **Signal Detection**
   - **Golden Cross (BUY)**: Short EMA crosses above Long EMA
   - **Death Cross (SELL)**: Short EMA crosses below Long EMA
   - Detects crossovers by comparing current vs previous EMA relationships

5. **Logging & P&L**
   - Logs every signal with comprehensive data
   - Tracks positions and calculates P&L
   - Maintains cumulative performance metrics

## 🔧 Configuration System

### `config/config.json` Structure:

```json
{
  "dhan_credentials": {
    "client_id": "YOUR_CLIENT_ID",
    "access_token": "YOUR_ACCESS_TOKEN"
  },
  "instrument": {
    "name": "NIFTY 50",
    "security_id": "13",
    "exchange_segment": "IDX_I"
  },
  "ema_combinations": [
    {"short_ema": 5, "long_ema": 10},
    {"short_ema": 8, "long_ema": 21},
    {"short_ema": 12, "long_ema": 26}
  ],
  "timeframes": ["1min", "5min", "10min"],
  "trading": {
    "initial_capital": 100000,
    "position_size": 1
  }
}
```

## 📈 Trading Logic

### EMA Crossover Strategy

The system implements a classic EMA crossover strategy:

1. **Signal Generation**:
   - Monitor multiple EMA pairs simultaneously
   - Detect when short-term EMA crosses long-term EMA
   - Generate BUY signal on Golden Cross
   - Generate SELL signal on Death Cross

2. **Position Management**:
   - Track separate positions for each timeframe and EMA combination
   - Calculate P&L based on entry/exit prices
   - Maintain running totals for performance analysis

3. **Risk Considerations**:
   - No position sizing logic (educational system)
   - No stop-loss or take-profit implementation
   - Signals are logged but not executed automatically

## 🔄 Real-time Processing

### Tick Processing Pipeline:

1. **WebSocket Handler**:
   ```python
   def _on_message(self, ws, message):
       data = json.loads(message)
       self._process_tick_data(data)
   ```

2. **Candle Building**:
   ```python
   def _process_tick_for_timeframe(self, timestamp, price, volume, timeframe):
       candle_start = self._get_candle_start_time(timestamp, interval_seconds)
       if new_candle_needed:
           self._complete_candle(timeframe)
           self.current_candles[timeframe] = OHLCCandle(candle_start, timeframe)
       self.current_candles[timeframe].update_tick(price, volume)
   ```

3. **EMA Updates**:
   ```python
   def _calculate_ema(self, timeframe, period, price):
       if not initialized:
           return sum(prices[-period:]) / period  # SMA for initialization
       else:
           return (price * multiplier) + (previous_ema * (1 - multiplier))
   ```

## 📁 Output Format

### CSV Structure:
```
Datetime,Action,Price,EMA_Combo,Entry_Exit,PnL,Short_EMA_Value,Long_EMA_Value,Candle_Open,Candle_High,Candle_Low,Candle_Close,Candle_Volume,Cumulative_PnL
```

### Sample Output:
```
2025-01-01 09:15:00,BUY,19500.50,5/10,ENTRY,0.00,19498.25,19495.80,19499.00,19501.25,19498.75,19500.50,1250,0.00
2025-01-01 09:18:00,SELL,19485.75,5/10,ENTRY,-14.75,19490.60,19493.45,19488.25,19490.00,19484.50,19485.75,980,-14.75
```

## 🚀 Performance Optimizations

1. **Memory Management**:
   - Uses `deque` with `maxlen` for historical data
   - Automatic cleanup of old data points
   - Efficient O(1) append operations

2. **Threading**:
   - WebSocket runs in separate thread
   - Heartbeat monitoring in background
   - Thread-safe CSV writing with locks

3. **Error Handling**:
   - Graceful WebSocket reconnection
   - Comprehensive exception handling
   - Automatic recovery from network issues

## 🧪 Testing & Development

### Mock Mode:
- Simulates real-time NIFTY 50 data
- No external dependencies required
- Perfect for strategy testing and development

### Test Coverage:
- EMA calculation accuracy
- Crossover detection logic
- CSV logging functionality
- Mock data generation

## 📊 Daily Usage Workflow

### Pre-Market (8:30 AM - 9:15 AM):
1. Update configuration if needed
2. Start the system: `python src/main.py`
3. Verify WebSocket connection
4. Check log files for any issues

### Market Hours (9:15 AM - 3:30 PM):
1. Monitor console output for signals
2. System runs automatically
3. CSV files updated in real-time
4. Check `logs/` directory for system health

### Post-Market (After 3:30 PM):
1. System continues running (can be stopped)
2. Analyze CSV files for performance
3. Review signal quality and frequency
4. Plan strategy adjustments

## 🔍 Monitoring & Debugging

### Log Files:
- `logs/ema_system_YYYYMMDD.log` - System logs
- Console output shows real-time signals
- CSV files contain all trading data

### Key Metrics to Monitor:
- WebSocket connection status
- Tick reception rate
- Signal generation frequency
- EMA calculation accuracy
- P&L performance

## ⚠️ Important Considerations

### Market Data:
- Requires active DhanHQ subscription
- NIFTY 50 is an index (not directly tradable)
- Use for analysis and signal generation only

### Risk Management:
- This is an educational/analysis system
- No automatic trade execution
- Implement proper risk management for live trading
- Always test thoroughly before any real trading

### Compliance:
- Ensure compliance with local regulations
- Understand tax implications of trading
- Consider professional financial advice

## 🔧 Customization Options

### Adding New Instruments:
1. Find security ID from DhanHQ instrument list
2. Update `config.json` with new instrument details
3. System automatically adapts to new instrument

### New EMA Combinations:
1. Add to `ema_combinations` in config
2. System calculates all combinations automatically
3. Separate signals generated for each combination

### Additional Timeframes:
1. Add to `timeframes` array in config
2. Supported: 1min, 5min, 10min, 15min, 30min, 1hour
3. Separate CSV files created for each timeframe

This system provides a solid foundation for EMA-based trading analysis and can be extended with additional indicators, risk management, and automated execution capabilities.
