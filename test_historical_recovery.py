#!/usr/bin/env python3
"""
Test Historical Recovery
========================

Test the historical data recovery system to verify it can fetch
and reconstruct EMA crossovers from market open to current time.
"""

import sys
import os
import json
from datetime import datetime, timedelta

# Add src directory to path
sys.path.append('src')

from ema import EMACalculator
from strategy import EMAStrategy
from logger import SignalLogger
from state_manager import StateManager
from market_hours import MarketHoursManager
from historical_data import HistoricalDataRecovery


def test_historical_recovery():
    """Test historical data recovery functionality"""
    print("🧪 Testing Historical Data Recovery...")
    
    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)
    
    # Create components
    market_hours_manager = MarketHoursManager(
        config=config.get('market_hours', {}),
        data_directory="test_data"
    )
    
    historical_recovery = HistoricalDataRecovery(
        dhan_credentials=config['dhan_credentials'],
        market_hours_config=config.get('market_hours', {})
    )
    
    state_manager = StateManager("test_data")
    ema_calculator = EMACalculator(config['ema_combinations'])
    signal_logger = SignalLogger("test_data", 100000)
    
    strategy = EMAStrategy(
        ema_combinations=config['ema_combinations'],
        timeframes=config['timeframes'],
        ema_calculator=ema_calculator,
        signal_logger=signal_logger,
        state_manager=state_manager
    )
    
    # Test market times
    market_start, current_time = historical_recovery.get_today_market_times()
    print(f"  Market start: {market_start.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"  Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    
    market_duration = (current_time - market_start).total_seconds() / 3600
    print(f"  Market duration: {market_duration:.1f} hours")
    
    # Test recovery need check
    recovery_needed = historical_recovery.is_recovery_needed(signal_logger)
    print(f"  Recovery needed: {recovery_needed}")
    
    if recovery_needed:
        print("\n📈 Attempting historical data recovery...")
        
        # Attempt to recover historical crossovers
        crossovers_found = historical_recovery.recover_historical_crossovers(
            security_id=config['instrument']['security_id'],
            exchange_segment=config['instrument']['exchange_segment'],
            ema_calculator=ema_calculator,
            signal_logger=signal_logger,
            strategy=strategy
        )
        
        print(f"  Crossovers recovered: {crossovers_found}")
        
        # Check results
        final_stats = signal_logger.get_statistics()
        print(f"  Total signals: {final_stats['total_signals']}")
        print(f"  Cumulative P&L: {final_stats['cumulative_pnl']:.2f}")
        
        # Show EMA state
        ema_values = ema_calculator.get_current_ema_values('1min')
        if ema_values:
            print(f"  Current EMAs: {ema_values}")
        
        return crossovers_found > 0
    else:
        print("  No recovery needed at this time")
        return True


def test_mock_data_generation():
    """Test mock historical data generation"""
    print("\n🧪 Testing Mock Data Generation...")
    
    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)
    
    historical_recovery = HistoricalDataRecovery(
        dhan_credentials=config['dhan_credentials'],
        market_hours_config=config.get('market_hours', {})
    )
    
    # Generate mock data for last 2 hours
    current_time = datetime.now(historical_recovery.timezone)
    start_time = current_time - timedelta(hours=2)
    
    print(f"  Generating mock data from {start_time.strftime('%H:%M')} to {current_time.strftime('%H:%M')}")
    
    mock_candles = historical_recovery.generate_mock_historical_candles(
        start_time, current_time, base_price=24750.0
    )
    
    print(f"  Generated {len(mock_candles)} mock candles")
    
    if mock_candles:
        first_candle = mock_candles[0]
        last_candle = mock_candles[-1]
        
        print(f"  First candle: {first_candle['timestamp'].strftime('%H:%M')} - Close: {first_candle['close']:.2f}")
        print(f"  Last candle: {last_candle['timestamp'].strftime('%H:%M')} - Close: {last_candle['close']:.2f}")
        
        # Calculate price change
        price_change = last_candle['close'] - first_candle['close']
        price_change_pct = (price_change / first_candle['close']) * 100
        
        print(f"  Price change: {price_change:+.2f} ({price_change_pct:+.2f}%)")
        
        return True
    
    return False


def test_csv_update():
    """Test CSV file update with historical data"""
    print("\n🧪 Testing CSV Update...")
    
    # Check current CSV file
    today = datetime.now().strftime("%Y%m%d")
    csv_file = f"test_data/nifty50_ema_signals_{today}.csv"
    
    print(f"  CSV file: {csv_file}")
    
    if os.path.exists(csv_file):
        with open(csv_file, 'r') as f:
            lines = f.readlines()
        
        print(f"  Current lines: {len(lines)} (including header)")
        
        if len(lines) > 1:
            print("  Recent signals:")
            for line in lines[-3:]:
                if line.strip() and not line.startswith('Date'):
                    fields = line.strip().split(',')
                    if len(fields) >= 4:
                        print(f"    {fields[1]} - {fields[2]} @ {fields[3]}")
        
        return len(lines) > 1
    else:
        print("  No CSV file found")
        return False


def main():
    """Run historical recovery tests"""
    print("=" * 60)
    print("TESTING HISTORICAL DATA RECOVERY")
    print("=" * 60)
    
    try:
        # Create test directories
        os.makedirs("test_data", exist_ok=True)
        
        # Run tests
        test1_success = test_historical_recovery()
        test2_success = test_mock_data_generation()
        test3_success = test_csv_update()
        
        print("\n" + "=" * 60)
        print("TEST RESULTS")
        print("=" * 60)
        
        if test1_success and test2_success:
            print("🎉 HISTORICAL RECOVERY TESTS PASSED!")
            print("\nThe enhanced system now provides:")
            print("  ✅ Historical data fetching from DhanHQ API")
            print("  ✅ Mock data generation for testing")
            print("  ✅ EMA crossover reconstruction from market open")
            print("  ✅ CSV update with historical signals")
            print("  ✅ Seamless continuation with live data")
            
            if test3_success:
                print("  ✅ CSV file contains historical signals")
            
            print("\n🚀 Now restart the system to see historical crossovers!")
            print("  python ema_daemon.py restart")
            
        else:
            print("⚠️  Some tests failed")
            if not test1_success:
                print("  - Historical recovery test failed")
            if not test2_success:
                print("  - Mock data generation failed")
            if not test3_success:
                print("  - CSV update test failed")
        
        print("\nNext steps:")
        print("1. Stop current system: python ema_daemon.py stop")
        print("2. Restart with recovery: python ema_daemon.py start")
        print("3. Check CSV for historical signals")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
