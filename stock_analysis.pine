// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © raghumys_nandan

//@version=6
indicator("EMA Crossover/Crossunder Event Tracking and Trend Zone Validation", overlay=true)

// === EMAs ===
ema5 = ta.ema(close, 5)
ema10 = ta.ema(close, 10)
ema35 = ta.ema(close, 35)

// === Colors ===
lightBlue = color.rgb(20, 100, 255)       // RGB values for Blue
magentaColor = color.rgb(255, 0, 255)     // RGB values for Magenta
darkBlue = color.rgb(100, 100, 210)       // RGB values for Dark Blue

// === EMA Plots ===
plot(ema5, color=lightBlue, linewidth=2, title="EMA 5")
plot(ema10, color=magentaColor, linewidth=2, title="EMA 10")
plot(ema35, color=darkBlue, linewidth=2, title="EMA 35")

// === Signals ===
isCrossover = ta.crossover(ema5, ema10)
isCrossunder = ta.crossunder(ema5, ema10)

// === State Variables ===
var float trackedLow = na
var int trackedLowBar = na
var float trackedHigh = na
var int trackedHighBar = na
var bool afterCrossunder = false
var bool afterCrossover = false

// === Arrays for Storing Zones ===
var line[] lowLines = array.new_line()
var label[] lowLabels = array.new_label()
var float[] lowPrices = array.new_float()
var int[] lowBars = array.new_int()

var line[] highLines = array.new_line()
var label[] highLabels = array.new_label()
var float[] highPrices = array.new_float()
var int[] highBars = array.new_int()

// === Live Tracking ===
if afterCrossunder
    if na(trackedLow) or low < trackedLow
        trackedLow := low
        trackedLowBar := bar_index

if afterCrossover
    if na(trackedHigh) or high > trackedHigh
        trackedHigh := high
        trackedHighBar := bar_index

// === Check for Break of Crossover Zones ===
for i = array.size(lowPrices) - 1 to 0
    lvl = array.get(lowPrices, i)
    if close < lvl
        line.delete(array.get(lowLines, i))
        label.delete(array.get(lowLabels, i))
        array.remove(lowLines, i)
        array.remove(lowLabels, i)
        array.remove(lowPrices, i)
        array.remove(lowBars, i)

// === Check for Break of Crossunder Zones ===
for i = array.size(highPrices) - 1 to 0
    lvl = array.get(highPrices, i)
    if close > lvl
        line.delete(array.get(highLines, i))
        label.delete(array.get(highLabels, i))
        array.remove(highLines, i)
        array.remove(highLabels, i)
        array.remove(highPrices, i)
        array.remove(highBars, i)

// === On Crossover ===
if isCrossover
    line.new(bar_index, ta.highest(high, 100), bar_index, ta.lowest(low, 100), color=color.green, width=1, style=line.style_dashed)

    if not na(trackedLow) and not na(trackedLowBar)
        l = line.new(trackedLowBar, trackedLow, bar_index, trackedLow, color=color.green, width=2)
        lb = label.new(trackedLowBar, trackedLow, text="↓", style=label.style_label_down, color=color.green, textcolor=color.white)
        array.push(lowLines, l)
        array.push(lowLabels, lb)
        array.push(lowPrices, trackedLow)
        array.push(lowBars, trackedLowBar)

        if array.size(lowPrices) > 50
            line.delete(array.shift(lowLines))
            label.delete(array.shift(lowLabels))
            array.shift(lowPrices)
            array.shift(lowBars)

    // Reset high tracking
    trackedHigh := na
    trackedHighBar := na
    afterCrossover := false
    afterCrossunder := true

// === On Crossunder ===
if isCrossunder
    line.new(bar_index, ta.highest(high, 100), bar_index, ta.lowest(low, 100), color=color.red, width=1, style=line.style_dashed)

    if not na(trackedHigh) and not na(trackedHighBar)
        l = line.new(trackedHighBar, trackedHigh, bar_index, trackedHigh, color=color.red, width=2)
        lb = label.new(trackedHighBar, trackedHigh, text="↑", style=label.style_label_up, color=color.red, textcolor=color.white)
        array.push(highLines, l)
        array.push(highLabels, lb)
        array.push(highPrices, trackedHigh)
        array.push(highBars, trackedHighBar)

        if array.size(highPrices) > 50
            line.delete(array.shift(highLines))
            label.delete(array.shift(highLabels))
            array.shift(highPrices)
            array.shift(highBars)

    // Reset low tracking
    trackedLow := na
    trackedLowBar := na
    afterCrossunder := false
    afterCrossover := true