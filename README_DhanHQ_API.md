# DhanHQ API Examples

This repository contains comprehensive examples for using the DhanHQ v2 APIs to fetch market data, manage portfolios, and execute trades.

## 📋 Prerequisites

1. **DhanHQ Account**: You need an active DhanHQ trading account
2. **API Access**: Enable API access from your DhanHQ web portal
3. **Python 3.7+**: Make sure you have Python installed

## 🚀 Quick Setup

### 1. Install Required Packages

```bash
pip install dhanhq requests pandas
```

### 2. Get Your API Credentials

1. Login to [web.dhan.co](https://web.dhan.co)
2. Go to **My Profile** → **Access DhanHQ APIs**
3. Copy your **Client ID** and **Access Token**

### 3. Update Credentials

Replace the following in the Python files:
```python
CLIENT_ID = "your_actual_client_id"
ACCESS_TOKEN = "your_actual_access_token"
```

## 📁 File Overview

### 1. `dhanhq_api_examples.py`
**Comprehensive API wrapper with all major functionalities**

Features:
- ✅ User profile management
- ✅ Real-time market data (LTP, OHLC, Market Depth)
- ✅ Historical data (Daily & Intraday)
- ✅ Portfolio management (Holdings, Positions)
- ✅ Custom API wrapper class
- ✅ Data conversion to pandas DataFrames

**Usage:**
```bash
python dhanhq_api_examples.py
```

### 2. `dhanhq_simple_example.py`
**Simple examples using the official DhanHQ Python client**

Features:
- ✅ Basic API calls using dhanhq library
- ✅ Market data fetching
- ✅ Portfolio information
- ✅ Order management examples (commented for safety)
- ✅ Instrument information guide

**Usage:**
```bash
python dhanhq_simple_example.py
```

### 3. `dhanhq_focused_examples.py`
**Interactive trading assistant with menu-driven interface**

Features:
- ✅ Real-time stock monitoring
- ✅ Technical analysis with moving averages
- ✅ Portfolio performance analysis
- ✅ Market overview dashboard
- ✅ Interactive menu system

**Usage:**
```bash
python dhanhq_focused_examples.py
```

## 🔧 API Endpoints Covered

### Data APIs
| API | Description | Rate Limit |
|-----|-------------|------------|
| `/profile` | User profile and token validity | 20/sec |
| `/marketfeed/ltp` | Last Traded Price | 1/sec |
| `/marketfeed/ohlc` | OHLC data | 1/sec |
| `/marketfeed/quote` | Market depth | 1/sec |
| `/charts/historical` | Historical daily data | 5/sec |
| `/charts/intraday` | Intraday minute data | 5/sec |

### Trading APIs
| API | Description | Rate Limit |
|-----|-------------|------------|
| `/holdings` | Demat holdings | 20/sec |
| `/positions` | Open positions | 20/sec |
| `/orders` | Order management | 25/sec |
| `/funds` | Fund limits | 20/sec |

## 📊 Common Security IDs

| Stock | Security ID | Exchange |
|-------|-------------|----------|
| TCS | 1333 | NSE_EQ |
| Reliance | 11536 | NSE_EQ |
| Infosys | 1594 | NSE_EQ |
| HDFC Bank | 1330 | NSE_EQ |
| ICICI Bank | 4963 | NSE_EQ |

**Note**: Security IDs may change. Always refer to the [official instrument list](https://dhanhq.co/docs/v2/instruments/).

## 🔍 Exchange Segments

- `NSE_EQ`: NSE Equity
- `NSE_FNO`: NSE Futures & Options
- `BSE_EQ`: BSE Equity
- `BSE_FNO`: BSE Futures & Options
- `NSE_CURRENCY`: NSE Currency
- `MCX_COMM`: MCX Commodity

## 💡 Usage Examples

### Get Real-time Stock Price
```python
from dhanhq import dhanhq

dhan = dhanhq("client_id", "access_token")

# Get LTP for TCS
ltp_data = dhan.get_ltp_data(
    exchange_segment=dhan.NSE_EQ,
    security_id_list=[1333]
)
print(f"TCS Price: ₹{ltp_data['data']['NSE_EQ']['1333']['last_price']}")
```

### Get Historical Data
```python
# Get 30 days historical data for TCS
historical_data = dhan.get_historical_data(
    symbol="TCS",
    exchange_segment=dhan.NSE_EQ,
    instrument_type=dhan.EQUITY,
    expiry_code=0,
    from_date="2024-01-01",
    to_date="2024-01-31"
)
```

### Check Portfolio
```python
# Get holdings
holdings = dhan.get_holdings()
for holding in holdings:
    print(f"{holding['tradingSymbol']}: {holding['totalQty']} shares")

# Get positions
positions = dhan.get_positions()
for position in positions:
    if position['netQty'] != 0:
        print(f"{position['tradingSymbol']}: P&L ₹{position['unrealizedProfit']}")
```

## ⚠️ Important Notes

### Rate Limits
- **Order APIs**: 25 requests/second
- **Data APIs**: 5 requests/second  
- **Quote APIs**: 1 request/second
- **Non-Trading APIs**: 20 requests/second

### Data Limits
- **Market Quote**: Up to 1000 instruments per request
- **Intraday Data**: Maximum 90 days per request
- **Historical Data**: Available from instrument inception

### Security
- ✅ Never share your access token
- ✅ Use environment variables for credentials in production
- ✅ Regenerate tokens periodically
- ✅ Test with small quantities first

## 🛠️ Troubleshooting

### Common Issues

1. **Authentication Error**
   - Verify your Client ID and Access Token
   - Check token validity using profile API

2. **Rate Limit Exceeded**
   - Add delays between API calls
   - Use batch requests where possible

3. **Invalid Security ID**
   - Check the [instrument list](https://dhanhq.co/docs/v2/instruments/)
   - Verify exchange segment

4. **No Data Returned**
   - Check market hours
   - Verify instrument is actively traded

### Error Codes
```python
# Common error handling
try:
    data = dhan.get_ltp_data(exchange_segment=dhan.NSE_EQ, security_id_list=[1333])
except Exception as e:
    print(f"API Error: {e}")
```

## 📚 Additional Resources

- [DhanHQ API Documentation](https://dhanhq.co/docs/v2/)
- [Python Client Documentation](https://pypi.org/project/dhanhq/)
- [Instrument Master Files](https://dhanhq.co/docs/v2/instruments/)
- [API Sandbox](https://api.dhan.co/v2/#)

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## ⚖️ Disclaimer

This code is for educational purposes. Always test thoroughly before using in live trading. Trading involves risk of financial loss.

---

**Happy Trading! 🚀📈**

curl --request POST \
--url https://api.dhan.co/charts/historical \
--header 'Content-Type: application/json' \
--header 'access-token: JWT' \
--data '{}'

